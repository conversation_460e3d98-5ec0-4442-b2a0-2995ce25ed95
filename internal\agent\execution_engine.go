package agent

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ExecutionEngine Agent执行引擎
type ExecutionEngine struct {
	registry   *AgentRegistry
	logger     *logrus.Logger
	config     *ExecutionConfig
	executions map[string]*ExecutionSession
	mutex      sync.RWMutex
	eventBus   *EventBus
}

// ExecutionConfig 执行引擎配置
type ExecutionConfig struct {
	MaxConcurrentExecutions int           `json:"max_concurrent_executions"`
	DefaultTimeout          time.Duration `json:"default_timeout"`
	EnableMetrics           bool          `json:"enable_metrics"`
	RetryEnabled            bool          `json:"retry_enabled"`
	MaxRetries              int           `json:"max_retries"`
}

// ExecutionSession 执行会话
type ExecutionSession struct {
	ID             string                    `json:"id"`
	DecisionResult *DecisionResult           `json:"decision_result"`
	Context        *ExecutionContext         `json:"context"`
	Status         ExecutionSessionStatus    `json:"status"`
	Steps          map[string]*StepExecution `json:"steps"`
	Results        []*ExecutionResult        `json:"results"`
	StartTime      time.Time                 `json:"start_time"`
	EndTime        *time.Time                `json:"end_time,omitempty"`
	Error          string                    `json:"error,omitempty"`
	Metadata       map[string]interface{}    `json:"metadata"`
}

// ExecutionSessionStatus 执行会话状态
type ExecutionSessionStatus string

const (
	SessionStatusPending   ExecutionSessionStatus = "pending"
	SessionStatusRunning   ExecutionSessionStatus = "running"
	SessionStatusCompleted ExecutionSessionStatus = "completed"
	SessionStatusFailed    ExecutionSessionStatus = "failed"
	SessionStatusCancelled ExecutionSessionStatus = "cancelled"
)

// StepExecution 步骤执行
type StepExecution struct {
	Step      *ExecutionStep      `json:"step"`
	Status    StepExecutionStatus `json:"status"`
	Result    *ExecutionResult    `json:"result,omitempty"`
	StartTime time.Time           `json:"start_time"`
	EndTime   *time.Time          `json:"end_time,omitempty"`
	Error     string              `json:"error,omitempty"`
	Retries   int                 `json:"retries"`
}

// StepExecutionStatus 步骤执行状态
type StepExecutionStatus string

const (
	StepStatusPending   StepExecutionStatus = "pending"
	StepStatusRunning   StepExecutionStatus = "running"
	StepStatusCompleted StepExecutionStatus = "completed"
	StepStatusFailed    StepExecutionStatus = "failed"
	StepStatusSkipped   StepExecutionStatus = "skipped"
)

// NewExecutionEngine 创建执行引擎
func NewExecutionEngine(registry *AgentRegistry, eventBus *EventBus, logger *logrus.Logger) *ExecutionEngine {
	config := &ExecutionConfig{
		MaxConcurrentExecutions: 10,
		DefaultTimeout:          5 * time.Minute,
		EnableMetrics:           true,
		RetryEnabled:            true,
		MaxRetries:              3,
	}

	return &ExecutionEngine{
		registry:   registry,
		logger:     logger,
		config:     config,
		executions: make(map[string]*ExecutionSession),
		eventBus:   eventBus,
	}
}

// ExecuteDecision 执行决策结果
func (ee *ExecutionEngine) ExecuteDecision(ctx context.Context, decision *DecisionResult, execCtx *ExecutionContext) (*ExecutionSession, error) {
	sessionID := generateID()

	ee.logger.WithFields(logrus.Fields{
		"session_id":   sessionID,
		"agents_count": len(decision.Agents),
		"strategy":     decision.ExecutionPlan.Strategy,
	}).Info("Starting decision execution")

	// 检查并发执行限制
	if err := ee.checkConcurrencyLimit(); err != nil {
		return nil, err
	}

	// 创建执行会话
	session := &ExecutionSession{
		ID:             sessionID,
		DecisionResult: decision,
		Context:        execCtx,
		Status:         SessionStatusPending,
		Steps:          make(map[string]*StepExecution),
		Results:        make([]*ExecutionResult, 0),
		StartTime:      time.Now(),
		Metadata:       make(map[string]interface{}),
	}

	// 初始化执行步骤
	for _, step := range decision.ExecutionPlan.Steps {
		session.Steps[step.ID] = &StepExecution{
			Step:      &step,
			Status:    StepStatusPending,
			StartTime: time.Now(),
		}
	}

	// 注册执行会话
	ee.mutex.Lock()
	ee.executions[sessionID] = session
	ee.mutex.Unlock()

	// 发送执行开始事件
	ee.eventBus.Publish(&AgentEvent{
		Type:      EventTypeAgentExecutionStart,
		AgentID:   "execution_engine",
		Timestamp: time.Now(),
		Data:      session,
		TraceID:   execCtx.TraceID,
	})

	// 异步执行
	go ee.executeSession(ctx, session)

	return session, nil
}

// executeSession 执行会话
func (ee *ExecutionEngine) executeSession(ctx context.Context, session *ExecutionSession) {
	defer func() {
		if r := recover(); r != nil {
			ee.logger.WithFields(logrus.Fields{
				"session_id": session.ID,
				"error":      r,
			}).Error("Execution session panicked")

			session.Status = SessionStatusFailed
			session.Error = fmt.Sprintf("execution panicked: %v", r)
			endTime := time.Now()
			session.EndTime = &endTime
		}

		// 发送执行结束事件
		ee.eventBus.Publish(&AgentEvent{
			Type:      EventTypeAgentExecutionEnd,
			AgentID:   "execution_engine",
			Timestamp: time.Now(),
			Data:      session,
			TraceID:   session.Context.TraceID,
		})
	}()

	session.Status = SessionStatusRunning

	// 根据执行策略执行
	var err error
	switch session.DecisionResult.ExecutionPlan.Strategy {
	case StrategySequential:
		err = ee.executeSequential(ctx, session)
	case StrategyParallel:
		err = ee.executeParallel(ctx, session)
	case StrategyConditional:
		err = ee.executeConditional(ctx, session)
	case StrategyPipeline:
		err = ee.executePipeline(ctx, session)
	default:
		err = fmt.Errorf("unsupported execution strategy: %s", session.DecisionResult.ExecutionPlan.Strategy)
	}

	// 更新会话状态
	endTime := time.Now()
	session.EndTime = &endTime

	if err != nil {
		session.Status = SessionStatusFailed
		session.Error = err.Error()
		ee.logger.WithError(err).WithField("session_id", session.ID).Error("Execution session failed")
	} else {
		session.Status = SessionStatusCompleted
		ee.logger.WithField("session_id", session.ID).Info("Execution session completed successfully")
	}
}

// executeSequential 顺序执行
func (ee *ExecutionEngine) executeSequential(ctx context.Context, session *ExecutionSession) error {
	steps := session.DecisionResult.ExecutionPlan.Steps

	for _, step := range steps {
		stepExec := session.Steps[step.ID]

		// 检查执行条件
		if step.Condition != "" {
			if !ee.evaluateCondition(step.Condition, session) {
				stepExec.Status = StepStatusSkipped
				continue
			}
		}

		// 执行步骤
		if err := ee.executeStep(ctx, stepExec, session); err != nil {
			return fmt.Errorf("step %s failed: %w", step.ID, err)
		}

		// 检查是否应该继续
		if stepExec.Status == StepStatusFailed {
			return fmt.Errorf("step %s failed, stopping sequential execution", step.ID)
		}
	}

	return nil
}

// executeParallel 并行执行
func (ee *ExecutionEngine) executeParallel(ctx context.Context, session *ExecutionSession) error {
	steps := session.DecisionResult.ExecutionPlan.Steps
	var wg sync.WaitGroup
	var mu sync.Mutex
	var firstError error

	for _, step := range steps {
		stepExec := session.Steps[step.ID]

		// 检查执行条件
		if step.Condition != "" {
			if !ee.evaluateCondition(step.Condition, session) {
				stepExec.Status = StepStatusSkipped
				continue
			}
		}

		wg.Add(1)
		go func(se *StepExecution) {
			defer wg.Done()

			if err := ee.executeStep(ctx, se, session); err != nil {
				mu.Lock()
				if firstError == nil {
					firstError = err
				}
				mu.Unlock()
			}
		}(stepExec)
	}

	wg.Wait()
	return firstError
}

// executeConditional 条件执行
func (ee *ExecutionEngine) executeConditional(ctx context.Context, session *ExecutionSession) error {
	// 根据依赖关系和条件执行步骤
	dependencies := session.DecisionResult.ExecutionPlan.Dependencies
	executed := make(map[string]bool)

	for len(executed) < len(session.Steps) {
		progress := false

		for stepID, stepExec := range session.Steps {
			if executed[stepID] {
				continue
			}

			// 检查依赖是否满足
			if ee.areDependenciesSatisfied(stepID, dependencies, executed, session) {
				if err := ee.executeStep(ctx, stepExec, session); err != nil {
					return fmt.Errorf("step %s failed: %w", stepID, err)
				}
				executed[stepID] = true
				progress = true
			}
		}

		if !progress {
			return fmt.Errorf("circular dependency detected or unresolvable conditions")
		}
	}

	return nil
}

// executePipeline 管道执行
func (ee *ExecutionEngine) executePipeline(ctx context.Context, session *ExecutionSession) error {
	// 管道执行：前一个步骤的输出作为下一个步骤的输入
	steps := session.DecisionResult.ExecutionPlan.Steps
	var pipelineData interface{}

	for _, step := range steps {
		stepExec := session.Steps[step.ID]

		// 将管道数据添加到参数中
		if pipelineData != nil {
			if stepExec.Step.Parameters == nil {
				stepExec.Step.Parameters = make(map[string]interface{})
			}
			stepExec.Step.Parameters["pipeline_input"] = pipelineData
		}

		if err := ee.executeStep(ctx, stepExec, session); err != nil {
			return fmt.Errorf("pipeline step %s failed: %w", step.ID, err)
		}

		// 获取输出作为下一步的输入
		if stepExec.Result != nil {
			pipelineData = stepExec.Result.Data
		}
	}

	return nil
}

// executeStep 执行单个步骤
func (ee *ExecutionEngine) executeStep(ctx context.Context, stepExec *StepExecution, session *ExecutionSession) error {
	step := stepExec.Step
	stepExec.Status = StepStatusRunning
	stepExec.StartTime = time.Now()

	ee.logger.WithFields(logrus.Fields{
		"session_id": session.ID,
		"step_id":    step.ID,
		"agent_id":   step.AgentID,
		"capability": step.Capability,
	}).Info("Executing step")

	// 获取Agent
	agent, err := ee.registry.GetAgent(step.AgentID)
	if err != nil {
		stepExec.Status = StepStatusFailed
		stepExec.Error = err.Error()
		return err
	}

	// 创建执行请求
	request := &ExecutionRequest{
		ID:         generateID(),
		AgentID:    step.AgentID,
		Capability: step.Capability,
		Parameters: step.Parameters,
		Context:    session.Context,
		Options: &ExecutionOptions{
			Timeout:    30 * time.Second,
			RetryCount: ee.config.MaxRetries,
			RetryDelay: 2 * time.Second,
		},
		CreatedAt:   time.Now(),
		RequestedBy: "execution_engine",
	}

	// 执行Agent
	result, err := agent.Execute(ctx, request)
	endTime := time.Now()
	stepExec.EndTime = &endTime

	if err != nil {
		stepExec.Status = StepStatusFailed
		stepExec.Error = err.Error()

		// 重试逻辑
		if ee.config.RetryEnabled && stepExec.Retries < ee.config.MaxRetries {
			stepExec.Retries++
			ee.logger.WithFields(logrus.Fields{
				"session_id": session.ID,
				"step_id":    step.ID,
				"retry":      stepExec.Retries,
			}).Warn("Retrying failed step")

			time.Sleep(time.Duration(stepExec.Retries) * 2 * time.Second)
			return ee.executeStep(ctx, stepExec, session)
		}

		return err
	}

	stepExec.Status = StepStatusCompleted
	stepExec.Result = result
	session.Results = append(session.Results, result)

	ee.logger.WithFields(logrus.Fields{
		"session_id": session.ID,
		"step_id":    step.ID,
		"success":    result.Success,
		"duration":   endTime.Sub(stepExec.StartTime),
	}).Info("Step execution completed")

	return nil
}

// evaluateCondition 评估条件
func (ee *ExecutionEngine) evaluateCondition(condition string, session *ExecutionSession) bool {
	// 简单的条件评估实现
	// 实际项目中可以使用更复杂的表达式引擎
	return true
}

// areDependenciesSatisfied 检查依赖是否满足
func (ee *ExecutionEngine) areDependenciesSatisfied(stepID string, dependencies []Dependency, executed map[string]bool, session *ExecutionSession) bool {
	for _, dep := range dependencies {
		if dep.StepID == stepID {
			if !executed[dep.DependsOn] {
				return false
			}

			// 检查依赖类型
			if dep.Type == "success" {
				if stepExec, exists := session.Steps[dep.DependsOn]; exists {
					if stepExec.Status != StepStatusCompleted || (stepExec.Result != nil && !stepExec.Result.Success) {
						return false
					}
				}
			}
		}
	}
	return true
}

// checkConcurrencyLimit 检查并发限制
func (ee *ExecutionEngine) checkConcurrencyLimit() error {
	ee.mutex.RLock()
	defer ee.mutex.RUnlock()

	runningCount := 0
	for _, session := range ee.executions {
		if session.Status == SessionStatusRunning {
			runningCount++
		}
	}

	if runningCount >= ee.config.MaxConcurrentExecutions {
		return fmt.Errorf("maximum concurrent executions reached: %d", ee.config.MaxConcurrentExecutions)
	}

	return nil
}

// GetExecutionSession 获取执行会话
func (ee *ExecutionEngine) GetExecutionSession(sessionID string) (*ExecutionSession, error) {
	ee.mutex.RLock()
	defer ee.mutex.RUnlock()

	session, exists := ee.executions[sessionID]
	if !exists {
		return nil, fmt.Errorf("execution session not found: %s", sessionID)
	}

	return session, nil
}

// CancelExecution 取消执行
func (ee *ExecutionEngine) CancelExecution(sessionID string) error {
	ee.mutex.Lock()
	defer ee.mutex.Unlock()

	session, exists := ee.executions[sessionID]
	if !exists {
		return fmt.Errorf("execution session not found: %s", sessionID)
	}

	if session.Status != SessionStatusRunning {
		return fmt.Errorf("execution session is not running: %s", session.Status)
	}

	session.Status = SessionStatusCancelled
	endTime := time.Now()
	session.EndTime = &endTime

	ee.logger.WithField("session_id", sessionID).Info("Execution session cancelled")

	return nil
}

// GetExecutionStatistics 获取执行统计
func (ee *ExecutionEngine) GetExecutionStatistics() map[string]interface{} {
	ee.mutex.RLock()
	defer ee.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_sessions": len(ee.executions),
		"by_status":      make(map[ExecutionSessionStatus]int),
	}

	statusCounts := stats["by_status"].(map[ExecutionSessionStatus]int)
	for _, session := range ee.executions {
		statusCounts[session.Status]++
	}

	return stats
}
