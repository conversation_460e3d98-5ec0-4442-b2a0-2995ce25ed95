package agent

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ExecutionMonitor 执行监控器
type ExecutionMonitor struct {
	config           *MonitorConfig
	logger           *logrus.Logger
	activeExecutions map[string]*ExecutionSession
	alerts           chan *ExecutionAlert
	metrics          *ExecutionMetrics
	mutex            sync.RWMutex
	isRunning        bool
	stopChan         chan struct{}
}

// MonitorConfig 监控配置
type MonitorConfig struct {
	Enabled                bool    `json:"enabled"`
	MonitorInterval        int     `json:"monitor_interval"`   // 秒
	AlertThreshold         int     `json:"alert_threshold"`    // 秒
	MaxExecutionTime       int     `json:"max_execution_time"` // 秒
	MaxMemoryUsageMB       int64   `json:"max_memory_usage_mb"`
	MaxCPUPercent          float64 `json:"max_cpu_percent"`
	MaxDiskIORate          int64   `json:"max_disk_io_rate"`    // MB/s
	MaxNetworkIORate       int64   `json:"max_network_io_rate"` // MB/s
	EnableRealTimeAlerts   bool    `json:"enable_real_time_alerts"`
	EnableResourceTracking bool    `json:"enable_resource_tracking"`
	AlertChannelSize       int     `json:"alert_channel_size"`
	MetricsRetentionHours  int     `json:"metrics_retention_hours"`
}

// ExecutionSession 执行会话
type ExecutionSession struct {
	ID            string                  `json:"id"`
	RequestID     string                  `json:"request_id"`
	UserID        int64                   `json:"user_id"`
	SessionID     string                  `json:"session_id"`
	Command       string                  `json:"command"`
	Args          []string                `json:"args"`
	StartTime     time.Time               `json:"start_time"`
	LastUpdate    time.Time               `json:"last_update"`
	Status        string                  `json:"status"` // running, completed, failed, timeout, killed
	ProcessID     int                     `json:"process_id"`
	SandboxID     string                  `json:"sandbox_id,omitempty"`
	ResourceUsage *ExecutionResourceUsage `json:"resource_usage"`
	Alerts        []*ExecutionAlert       `json:"alerts"`
	Metadata      map[string]interface{}  `json:"metadata"`
	mutex         sync.RWMutex
}

// ExecutionResourceUsage 执行资源使用情况
type ExecutionResourceUsage struct {
	CurrentMemoryMB   float64   `json:"current_memory_mb"`
	PeakMemoryMB      float64   `json:"peak_memory_mb"`
	CurrentCPUPercent float64   `json:"current_cpu_percent"`
	AvgCPUPercent     float64   `json:"avg_cpu_percent"`
	DiskReadMB        float64   `json:"disk_read_mb"`
	DiskWriteMB       float64   `json:"disk_write_mb"`
	NetworkInMB       float64   `json:"network_in_mb"`
	NetworkOutMB      float64   `json:"network_out_mb"`
	SampleCount       int       `json:"sample_count"`
	LastSampleTime    time.Time `json:"last_sample_time"`
}

// ExecutionAlert 执行告警
type ExecutionAlert struct {
	ID          string                 `json:"id"`
	ExecutionID string                 `json:"execution_id"`
	Type        string                 `json:"type"`  // timeout, resource, security, error
	Level       string                 `json:"level"` // info, warning, error, critical
	Message     string                 `json:"message"`
	Details     map[string]interface{} `json:"details"`
	CreatedAt   time.Time              `json:"created_at"`
	Resolved    bool                   `json:"resolved"`
	ResolvedAt  *time.Time             `json:"resolved_at,omitempty"`
}

// ExecutionMetrics 执行指标
type ExecutionMetrics struct {
	TotalExecutions     int64                   `json:"total_executions"`
	ActiveExecutions    int                     `json:"active_executions"`
	CompletedExecutions int64                   `json:"completed_executions"`
	FailedExecutions    int64                   `json:"failed_executions"`
	TimeoutExecutions   int64                   `json:"timeout_executions"`
	TotalAlerts         int64                   `json:"total_alerts"`
	AvgExecutionTime    float64                 `json:"avg_execution_time"`
	PeakMemoryUsage     float64                 `json:"peak_memory_usage"`
	PeakCPUUsage        float64                 `json:"peak_cpu_usage"`
	LastUpdateTime      time.Time               `json:"last_update_time"`
	HourlyStats         map[string]*HourlyStats `json:"hourly_stats"`
	mutex               sync.RWMutex
}

// HourlyStats 小时统计
type HourlyStats struct {
	Hour           string  `json:"hour"`
	ExecutionCount int     `json:"execution_count"`
	SuccessRate    float64 `json:"success_rate"`
	AvgDuration    float64 `json:"avg_duration"`
	AlertCount     int     `json:"alert_count"`
	PeakMemoryMB   float64 `json:"peak_memory_mb"`
	PeakCPUPercent float64 `json:"peak_cpu_percent"`
}

// NewExecutionMonitor 创建执行监控器
func NewExecutionMonitor(config *MonitorConfig, logger *logrus.Logger) *ExecutionMonitor {
	if config == nil {
		config = DefaultMonitorConfig()
	}

	return &ExecutionMonitor{
		config:           config,
		logger:           logger,
		activeExecutions: make(map[string]*ExecutionSession),
		alerts:           make(chan *ExecutionAlert, config.AlertChannelSize),
		metrics:          NewExecutionMetrics(),
		isRunning:        false,
		stopChan:         make(chan struct{}),
	}
}

// DefaultMonitorConfig 默认监控配置
func DefaultMonitorConfig() *MonitorConfig {
	return &MonitorConfig{
		Enabled:                true,
		MonitorInterval:        1,    // 1秒
		AlertThreshold:         30,   // 30秒
		MaxExecutionTime:       300,  // 5分钟
		MaxMemoryUsageMB:       512,  // 512MB
		MaxCPUPercent:          80.0, // 80%
		MaxDiskIORate:          100,  // 100MB/s
		MaxNetworkIORate:       50,   // 50MB/s
		EnableRealTimeAlerts:   true,
		EnableResourceTracking: true,
		AlertChannelSize:       1000,
		MetricsRetentionHours:  24, // 24小时
	}
}

// NewExecutionMetrics 创建执行指标
func NewExecutionMetrics() *ExecutionMetrics {
	return &ExecutionMetrics{
		HourlyStats:    make(map[string]*HourlyStats),
		LastUpdateTime: time.Now(),
	}
}

// Start 启动执行监控器
func (em *ExecutionMonitor) Start(ctx context.Context) error {
	if !em.config.Enabled {
		em.logger.Info("Execution monitor is disabled, skipping start")
		return nil
	}

	em.mutex.Lock()
	defer em.mutex.Unlock()

	if em.isRunning {
		return fmt.Errorf("execution monitor is already running")
	}

	em.logger.Info("Starting execution monitor")
	em.isRunning = true

	// 启动监控协程
	go em.monitorRoutine(ctx)

	// 启动告警处理协程
	go em.alertRoutine(ctx)

	// 启动指标更新协程
	go em.metricsRoutine(ctx)

	em.logger.Info("Execution monitor started successfully")
	return nil
}

// Stop 停止执行监控器
func (em *ExecutionMonitor) Stop(ctx context.Context) error {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	if !em.isRunning {
		return nil
	}

	em.logger.Info("Stopping execution monitor")
	em.isRunning = false

	close(em.stopChan)
	close(em.alerts)

	em.logger.Info("Execution monitor stopped")
	return nil
}

// StartExecution 开始监控执行
func (em *ExecutionMonitor) StartExecution(req *ExecutionRequest) *ExecutionSession {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	session := &ExecutionSession{
		ID:         generateExecutionSessionID(),
		RequestID:  req.ID,
		UserID:     req.UserID,
		SessionID:  req.SessionID,
		Command:    req.Command,
		Args:       req.Args,
		StartTime:  time.Now(),
		LastUpdate: time.Now(),
		Status:     "running",
		ResourceUsage: &ExecutionResourceUsage{
			LastSampleTime: time.Now(),
		},
		Alerts:   []*ExecutionAlert{},
		Metadata: make(map[string]interface{}),
	}

	em.activeExecutions[session.ID] = session

	// 更新指标
	em.metrics.mutex.Lock()
	em.metrics.TotalExecutions++
	em.metrics.ActiveExecutions++
	em.metrics.mutex.Unlock()

	em.logger.WithFields(logrus.Fields{
		"execution_id": session.ID,
		"request_id":   req.ID,
		"command":      req.Command,
		"user_id":      req.UserID,
	}).Info("Started monitoring execution")

	return session
}

// EndExecution 结束监控执行
func (em *ExecutionMonitor) EndExecution(executionID string, result *ExecutionResult) {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	session, exists := em.activeExecutions[executionID]
	if !exists {
		return
	}

	session.mutex.Lock()
	session.Status = "completed"
	if !result.Success {
		session.Status = "failed"
	}
	session.LastUpdate = time.Now()
	session.mutex.Unlock()

	delete(em.activeExecutions, executionID)

	// 更新指标
	em.metrics.mutex.Lock()
	em.metrics.ActiveExecutions--
	if result.Success {
		em.metrics.CompletedExecutions++
	} else {
		em.metrics.FailedExecutions++
	}

	// 更新平均执行时间
	duration := result.Duration.Seconds()
	if em.metrics.TotalExecutions > 0 {
		em.metrics.AvgExecutionTime = (em.metrics.AvgExecutionTime*float64(em.metrics.TotalExecutions-1) + duration) / float64(em.metrics.TotalExecutions)
	}
	em.metrics.mutex.Unlock()

	em.logger.WithFields(logrus.Fields{
		"execution_id": executionID,
		"status":       session.Status,
		"duration":     duration,
	}).Info("Ended monitoring execution")
}

// monitorRoutine 监控协程
func (em *ExecutionMonitor) monitorRoutine(ctx context.Context) {
	ticker := time.NewTicker(time.Duration(em.config.MonitorInterval) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-em.stopChan:
			return
		case <-ticker.C:
			em.checkExecutions()
		}
	}
}

// checkExecutions 检查执行状态
func (em *ExecutionMonitor) checkExecutions() {
	em.mutex.RLock()
	sessions := make([]*ExecutionSession, 0, len(em.activeExecutions))
	for _, session := range em.activeExecutions {
		sessions = append(sessions, session)
	}
	em.mutex.RUnlock()

	for _, session := range sessions {
		em.checkExecution(session)
	}
}

// checkExecution 检查单个执行
func (em *ExecutionMonitor) checkExecution(session *ExecutionSession) {
	session.mutex.Lock()
	defer session.mutex.Unlock()

	now := time.Now()
	duration := now.Sub(session.StartTime)

	// 检查执行超时
	if duration.Seconds() > float64(em.config.MaxExecutionTime) {
		alert := &ExecutionAlert{
			ID:          generateAlertID(),
			ExecutionID: session.ID,
			Type:        "timeout",
			Level:       "error",
			Message:     fmt.Sprintf("Execution timeout after %v", duration),
			Details: map[string]interface{}{
				"max_time":    em.config.MaxExecutionTime,
				"actual_time": duration.Seconds(),
			},
			CreatedAt: now,
		}

		session.Alerts = append(session.Alerts, alert)
		em.sendAlert(alert)

		// 标记为超时
		session.Status = "timeout"

		// 更新指标
		em.metrics.mutex.Lock()
		em.metrics.TimeoutExecutions++
		em.metrics.mutex.Unlock()
	}

	// 更新资源使用情况
	if em.config.EnableResourceTracking {
		em.updateResourceUsage(session)
	}

	session.LastUpdate = now
}

// updateResourceUsage 更新资源使用情况
func (em *ExecutionMonitor) updateResourceUsage(session *ExecutionSession) {
	// 模拟资源使用情况更新
	// 在实际实现中，这里应该从系统中获取真实的资源使用数据

	usage := session.ResourceUsage
	usage.SampleCount++
	usage.LastSampleTime = time.Now()

	// 模拟当前资源使用
	currentMemory := 50.0 + float64(usage.SampleCount)*2.0
	currentCPU := 10.0 + float64(usage.SampleCount)*1.5

	usage.CurrentMemoryMB = currentMemory
	usage.CurrentCPUPercent = currentCPU

	// 更新峰值
	if currentMemory > usage.PeakMemoryMB {
		usage.PeakMemoryMB = currentMemory
	}

	// 更新平均值
	if usage.SampleCount > 0 {
		usage.AvgCPUPercent = (usage.AvgCPUPercent*float64(usage.SampleCount-1) + currentCPU) / float64(usage.SampleCount)
	}

	// 检查资源限制
	em.checkResourceLimits(session, usage)

	// 更新全局指标
	em.metrics.mutex.Lock()
	if currentMemory > em.metrics.PeakMemoryUsage {
		em.metrics.PeakMemoryUsage = currentMemory
	}
	if currentCPU > em.metrics.PeakCPUUsage {
		em.metrics.PeakCPUUsage = currentCPU
	}
	em.metrics.mutex.Unlock()
}

// checkResourceLimits 检查资源限制
func (em *ExecutionMonitor) checkResourceLimits(session *ExecutionSession, usage *ExecutionResourceUsage) {
	now := time.Now()

	// 检查内存限制
	if usage.CurrentMemoryMB > float64(em.config.MaxMemoryUsageMB) {
		alert := &ExecutionAlert{
			ID:          generateAlertID(),
			ExecutionID: session.ID,
			Type:        "resource",
			Level:       "warning",
			Message:     fmt.Sprintf("Memory usage %.2f MB exceeds limit %d MB", usage.CurrentMemoryMB, em.config.MaxMemoryUsageMB),
			Details: map[string]interface{}{
				"current_memory": usage.CurrentMemoryMB,
				"limit_memory":   em.config.MaxMemoryUsageMB,
				"resource_type":  "memory",
			},
			CreatedAt: now,
		}

		session.Alerts = append(session.Alerts, alert)
		em.sendAlert(alert)
	}

	// 检查CPU限制
	if usage.CurrentCPUPercent > em.config.MaxCPUPercent {
		alert := &ExecutionAlert{
			ID:          generateAlertID(),
			ExecutionID: session.ID,
			Type:        "resource",
			Level:       "warning",
			Message:     fmt.Sprintf("CPU usage %.2f%% exceeds limit %.2f%%", usage.CurrentCPUPercent, em.config.MaxCPUPercent),
			Details: map[string]interface{}{
				"current_cpu":   usage.CurrentCPUPercent,
				"limit_cpu":     em.config.MaxCPUPercent,
				"resource_type": "cpu",
			},
			CreatedAt: now,
		}

		session.Alerts = append(session.Alerts, alert)
		em.sendAlert(alert)
	}
}

// sendAlert 发送告警
func (em *ExecutionMonitor) sendAlert(alert *ExecutionAlert) {
	if !em.config.EnableRealTimeAlerts {
		return
	}

	select {
	case em.alerts <- alert:
		em.metrics.mutex.Lock()
		em.metrics.TotalAlerts++
		em.metrics.mutex.Unlock()
	default:
		em.logger.Warn("Alert channel is full, dropping alert")
	}
}

// alertRoutine 告警处理协程
func (em *ExecutionMonitor) alertRoutine(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-em.stopChan:
			return
		case alert, ok := <-em.alerts:
			if !ok {
				return
			}
			em.handleAlert(alert)
		}
	}
}

// handleAlert 处理告警
func (em *ExecutionMonitor) handleAlert(alert *ExecutionAlert) {
	em.logger.WithFields(logrus.Fields{
		"alert_id":     alert.ID,
		"execution_id": alert.ExecutionID,
		"type":         alert.Type,
		"level":        alert.Level,
		"message":      alert.Message,
	}).Warn("Execution alert triggered")

	// 在实际实现中，这里可以发送邮件、短信、Webhook等通知
}

// metricsRoutine 指标更新协程
func (em *ExecutionMonitor) metricsRoutine(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-em.stopChan:
			return
		case <-ticker.C:
			em.updateHourlyStats()
		}
	}
}

// updateHourlyStats 更新小时统计
func (em *ExecutionMonitor) updateHourlyStats() {
	em.metrics.mutex.Lock()
	defer em.metrics.mutex.Unlock()

	hour := time.Now().Format("2006-01-02-15")

	if _, exists := em.metrics.HourlyStats[hour]; !exists {
		em.metrics.HourlyStats[hour] = &HourlyStats{
			Hour: hour,
		}
	}

	stats := em.metrics.HourlyStats[hour]
	stats.ExecutionCount = int(em.metrics.TotalExecutions)

	if em.metrics.TotalExecutions > 0 {
		stats.SuccessRate = float64(em.metrics.CompletedExecutions) / float64(em.metrics.TotalExecutions) * 100
	}

	stats.AvgDuration = em.metrics.AvgExecutionTime
	stats.AlertCount = int(em.metrics.TotalAlerts)
	stats.PeakMemoryMB = em.metrics.PeakMemoryUsage
	stats.PeakCPUPercent = em.metrics.PeakCPUUsage

	em.metrics.LastUpdateTime = time.Now()

	// 清理过期的小时统计
	em.cleanupOldStats()
}

// cleanupOldStats 清理过期统计
func (em *ExecutionMonitor) cleanupOldStats() {
	cutoff := time.Now().Add(-time.Duration(em.config.MetricsRetentionHours) * time.Hour)
	cutoffHour := cutoff.Format("2006-01-02-15")

	for hour := range em.metrics.HourlyStats {
		if hour < cutoffHour {
			delete(em.metrics.HourlyStats, hour)
		}
	}
}

// GetMonitorStatus 获取监控状态
func (em *ExecutionMonitor) GetMonitorStatus() *MonitorStatus {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	sessions := make([]*ExecutionSession, 0, len(em.activeExecutions))
	for _, session := range em.activeExecutions {
		sessions = append(sessions, session)
	}

	return &MonitorStatus{
		Enabled:          em.config.Enabled,
		IsRunning:        em.isRunning,
		ActiveExecutions: sessions,
		Metrics:          em.metrics,
		Config:           em.config,
	}
}

// MonitorStatus 监控状态
type MonitorStatus struct {
	Enabled          bool                `json:"enabled"`
	IsRunning        bool                `json:"is_running"`
	ActiveExecutions []*ExecutionSession `json:"active_executions"`
	Metrics          *ExecutionMetrics   `json:"metrics"`
	Config           *MonitorConfig      `json:"config"`
}

// 辅助函数
func generateExecutionSessionID() string {
	return fmt.Sprintf("session_%d", time.Now().UnixNano())
}

func generateAlertID() string {
	return fmt.Sprintf("alert_%d", time.Now().UnixNano())
}
