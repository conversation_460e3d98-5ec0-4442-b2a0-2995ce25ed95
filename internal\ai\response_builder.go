package ai

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// responseBuilder 响应构建器实现
type responseBuilder struct {
	logger *logrus.Logger
}

// NewResponseBuilder 创建响应构建器
func NewResponseBuilder(logger *logrus.Logger) ResponseBuilder {
	return &responseBuilder{
		logger: logger,
	}
}

// BuildResponse 构建响应
func (rb *responseBuilder) BuildResponse(
	ctx context.Context,
	req *DualLayerRequest,
	classification *ClassificationResult,
	inference *InferenceResult,
	execution *ExecutionResult,
) (*DualLayerResponse, error) {
	rb.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"intent":     classification.Intent,
		"commands":   len(inference.Commands),
		"executed":   execution != nil,
	}).Info("Building dual-layer response")

	// 构建响应内容
	content := rb.buildResponseContent(classification, inference, execution)

	// 确定是否需要执行操作
	requiresAction := len(inference.Commands) > 0 && (execution == nil || !execution.Success)
	actionType := rb.determineActionType(classification.Intent, inference.Commands)

	response := &DualLayerResponse{
		Content:        content,
		Intent:         classification.Intent,
		Confidence:     classification.Confidence,
		Parameters:     inference.Parameters,
		Commands:       inference.Commands,
		ExecutionPlan:  inference.ExecutionPlan,
		Explanation:    inference.Explanation,
		RequiresAction: requiresAction,
		ActionType:     actionType,
		Timestamp:      time.Now(),
	}

	return response, nil
}

// BuildErrorResponse 构建错误响应
func (rb *responseBuilder) BuildErrorResponse(err error, stage string) (*DualLayerResponse, error) {
	rb.logger.WithFields(logrus.Fields{
		"error": err.Error(),
		"stage": stage,
	}).Error("Building error response")

	var content string
	var actionType string

	// 根据错误类型和阶段生成友好的错误消息
	switch stage {
	case "classification":
		content = "抱歉，我无法理解您的请求。请尝试更清楚地描述您需要执行的运维操作。"
		actionType = "clarification_needed"
	case "inference":
		content = "我理解了您的意图，但无法生成具体的执行步骤。请提供更多详细信息。"
		actionType = "more_info_needed"
	case "execution":
		content = "我已经理解了您的需求并生成了执行计划，但在执行过程中遇到了问题。请检查系统状态后重试。"
		actionType = "retry_suggested"
	case "response_building":
		content = "处理您的请求时遇到了内部错误，请稍后重试。"
		actionType = "system_error"
	default:
		content = "处理您的请求时遇到了未知错误，请联系系统管理员。"
		actionType = "system_error"
	}

	// 如果是DualLayerError，提供更详细的信息
	if dlErr, ok := err.(*DualLayerError); ok {
		content += fmt.Sprintf("\n\n错误详情：%s", dlErr.Details)
	}

	return &DualLayerResponse{
		Content:        content,
		Intent:         "error",
		Confidence:     0.0,
		Parameters:     make(map[string]interface{}),
		Commands:       make([]CommandSequence, 0),
		RequiresAction: false,
		ActionType:     actionType,
		Timestamp:      time.Now(),
	}, nil
}

// buildResponseContent 构建响应内容
func (rb *responseBuilder) buildResponseContent(
	classification *ClassificationResult,
	inference *InferenceResult,
	execution *ExecutionResult,
) string {
	var content strings.Builder

	// 根据意图类型构建不同的响应
	switch classification.Intent {
	case IntentConnectionDiagnosis:
		content.WriteString(rb.buildConnectionDiagnosisResponse(classification, inference, execution))
	case IntentCommandExecution:
		content.WriteString(rb.buildCommandExecutionResponse(classification, inference, execution))
	case IntentSystemMonitoring:
		content.WriteString(rb.buildSystemMonitoringResponse(classification, inference, execution))
	case IntentHostManagement:
		content.WriteString(rb.buildHostManagementResponse(classification, inference, execution))
	default:
		content.WriteString(rb.buildGenericResponse(classification, inference, execution))
	}

	return content.String()
}

// buildConnectionDiagnosisResponse 构建连接诊断响应
func (rb *responseBuilder) buildConnectionDiagnosisResponse(
	classification *ClassificationResult,
	inference *InferenceResult,
	execution *ExecutionResult,
) string {
	var content strings.Builder

	// 获取目标主机
	targetHost := rb.getTargetHost(classification.Entities)

	content.WriteString(fmt.Sprintf("🔍 **连接诊断报告 - %s**\n\n", targetHost))

	if execution != nil && execution.Success {
		content.WriteString("✅ **诊断完成**\n\n")
		content.WriteString(rb.formatExecutionResults(execution))
	} else if execution != nil && !execution.Success {
		content.WriteString("❌ **诊断发现问题**\n\n")
		content.WriteString(rb.formatExecutionResults(execution))
		content.WriteString("\n💡 **建议解决方案**：\n")
		content.WriteString(rb.generateConnectionTroubleshootingSuggestions(execution))
	} else {
		content.WriteString("📋 **诊断计划**\n\n")
		content.WriteString(inference.Explanation)
		content.WriteString("\n\n🚀 **即将执行的检查步骤**：\n")
		for i, cmd := range inference.Commands {
			content.WriteString(fmt.Sprintf("%d. %s\n", i+1, cmd.Description))
		}
		content.WriteString("\n请确认是否执行这些诊断步骤？")
	}

	return content.String()
}

// buildCommandExecutionResponse 构建命令执行响应
func (rb *responseBuilder) buildCommandExecutionResponse(
	classification *ClassificationResult,
	inference *InferenceResult,
	execution *ExecutionResult,
) string {
	var content strings.Builder

	content.WriteString("⚡ **命令执行**\n\n")

	if execution != nil {
		if execution.Success {
			content.WriteString("✅ **执行成功**\n\n")
		} else {
			content.WriteString("❌ **执行失败**\n\n")
		}
		content.WriteString(rb.formatExecutionResults(execution))
	} else {
		content.WriteString("📋 **执行计划**\n\n")
		content.WriteString(inference.Explanation)
		content.WriteString("\n\n🚀 **待执行命令**：\n")
		for i, cmd := range inference.Commands {
			content.WriteString(fmt.Sprintf("%d. `%s`\n   %s\n", i+1, cmd.Command, cmd.Description))
		}
	}

	return content.String()
}

// buildSystemMonitoringResponse 构建系统监控响应
func (rb *responseBuilder) buildSystemMonitoringResponse(
	classification *ClassificationResult,
	inference *InferenceResult,
	execution *ExecutionResult,
) string {
	var content strings.Builder

	content.WriteString("📊 **系统监控**\n\n")

	if execution != nil {
		content.WriteString(rb.formatMonitoringResults(execution))
	} else {
		content.WriteString("📋 **监控计划**\n\n")
		content.WriteString(inference.Explanation)
	}

	return content.String()
}

// buildHostManagementResponse 构建主机管理响应
func (rb *responseBuilder) buildHostManagementResponse(
	classification *ClassificationResult,
	inference *InferenceResult,
	execution *ExecutionResult,
) string {
	var content strings.Builder

	content.WriteString("🖥️ **主机管理**\n\n")

	if execution != nil {
		if execution.Success {
			content.WriteString("✅ **操作成功**\n\n")
		} else {
			content.WriteString("❌ **操作失败**\n\n")
		}
		content.WriteString(rb.formatExecutionResults(execution))
	} else {
		content.WriteString(inference.Explanation)
	}

	return content.String()
}

// buildGenericResponse 构建通用响应
func (rb *responseBuilder) buildGenericResponse(
	classification *ClassificationResult,
	inference *InferenceResult,
	execution *ExecutionResult,
) string {
	var content strings.Builder

	content.WriteString(fmt.Sprintf("🤖 **AI助手** (意图: %s, 置信度: %.2f)\n\n", classification.Intent, classification.Confidence))

	if execution != nil {
		content.WriteString(rb.formatExecutionResults(execution))
	} else {
		content.WriteString(inference.Explanation)
	}

	return content.String()
}

// formatExecutionResults 格式化执行结果
func (rb *responseBuilder) formatExecutionResults(execution *ExecutionResult) string {
	var content strings.Builder

	for _, result := range execution.Results {
		if result.Success {
			content.WriteString(fmt.Sprintf("✅ **步骤 %d**: %s\n", result.Step, result.Command))
			if result.Output != "" {
				content.WriteString(fmt.Sprintf("```\n%s\n```\n", result.Output))
			}
		} else {
			content.WriteString(fmt.Sprintf("❌ **步骤 %d**: %s\n", result.Step, result.Command))
			if result.Error != "" {
				content.WriteString(fmt.Sprintf("**错误**: %s\n", result.Error))
			}
		}
		content.WriteString("\n")
	}

	if execution.Summary != "" {
		content.WriteString(fmt.Sprintf("📝 **总结**: %s\n", execution.Summary))
	}

	return content.String()
}

// formatMonitoringResults 格式化监控结果
func (rb *responseBuilder) formatMonitoringResults(execution *ExecutionResult) string {
	var content strings.Builder

	content.WriteString("📈 **监控数据**\n\n")

	for _, result := range execution.Results {
		if result.Success && result.Output != "" {
			content.WriteString(fmt.Sprintf("**%s**\n", result.Command))
			content.WriteString(fmt.Sprintf("```\n%s\n```\n\n", result.Output))
		}
	}

	return content.String()
}

// generateConnectionTroubleshootingSuggestions 生成连接故障排除建议
func (rb *responseBuilder) generateConnectionTroubleshootingSuggestions(execution *ExecutionResult) string {
	var suggestions strings.Builder

	for _, result := range execution.Results {
		if !result.Success {
			switch {
			case strings.Contains(result.Command, "nc -zv"):
				suggestions.WriteString("- 检查目标主机是否在线\n")
				suggestions.WriteString("- 确认SSH服务是否运行在端口22\n")
				suggestions.WriteString("- 检查防火墙设置\n")
			case strings.Contains(result.Command, "ssh"):
				suggestions.WriteString("- 验证SSH认证配置\n")
				suggestions.WriteString("- 检查用户名和密码/密钥\n")
				suggestions.WriteString("- 确认用户权限\n")
			case strings.Contains(result.Command, "ping"):
				suggestions.WriteString("- 检查网络连通性\n")
				suggestions.WriteString("- 确认IP地址正确\n")
				suggestions.WriteString("- 检查路由配置\n")
			}
		}
	}

	if suggestions.Len() == 0 {
		suggestions.WriteString("- 请联系系统管理员进行进一步诊断\n")
	}

	return suggestions.String()
}

// getTargetHost 获取目标主机
func (rb *responseBuilder) getTargetHost(entities map[string]interface{}) string {
	if ip, exists := entities[EntityIPAddress]; exists {
		if ipStr, ok := ip.(string); ok {
			return ipStr
		}
	}
	if hostname, exists := entities[EntityHostname]; exists {
		if hostnameStr, ok := hostname.(string); ok {
			return hostnameStr
		}
	}
	return "未知主机"
}

// determineActionType 确定操作类型
func (rb *responseBuilder) determineActionType(intent string, commands []CommandSequence) string {
	if len(commands) == 0 {
		return "information_only"
	}

	switch intent {
	case IntentConnectionDiagnosis:
		return "diagnosis"
	case IntentCommandExecution:
		return "command_execution"
	case IntentSystemMonitoring:
		return "monitoring"
	case IntentHostManagement:
		return "host_management"
	default:
		return "general_action"
	}
}
