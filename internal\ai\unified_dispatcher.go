package ai

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// unifiedDispatcher 统一调度器实现
type unifiedDispatcher struct {
	classifier      IntentClassifier
	inferenceEngine ParameterInferenceEngine
	executionEngine ExecutionEngine
	responseBuilder ResponseBuilder
	logger          *logrus.Logger

	// 处理状态管理
	processingStatus map[string]*ProcessingStatus
	statusMutex      sync.RWMutex

	// 配置
	config *DispatcherConfig
}

// DispatcherConfig 调度器配置
type DispatcherConfig struct {
	MaxProcessingTime time.Duration `json:"max_processing_time"`
	EnableAsync       bool          `json:"enable_async"`
	RetryAttempts     int           `json:"retry_attempts"`
	RetryDelay        time.Duration `json:"retry_delay"`
}

// ExecutionEngine 执行引擎接口
type ExecutionEngine interface {
	ExecuteCommands(ctx context.Context, commands []CommandSequence, plan *ExecutionPlan) (*ExecutionResult, error)
	GetExecutionStatus(executionID string) (*ExecutionStatus, error)
	CancelExecution(executionID string) error
}

// ResponseBuilder 响应构建器接口
type ResponseBuilder interface {
	BuildResponse(ctx context.Context, req *DualLayerRequest, classification *ClassificationResult, inference *InferenceResult, execution *ExecutionResult) (*DualLayerResponse, error)
	BuildErrorResponse(err error, stage string) (*DualLayerResponse, error)
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	Success  bool                   `json:"success"`
	Results  []CommandResult        `json:"results"`
	Summary  string                 `json:"summary"`
	Duration time.Duration          `json:"duration"`
	Metadata map[string]interface{} `json:"metadata"`
}

// CommandResult 命令结果
type CommandResult struct {
	Step     int           `json:"step"`
	Command  string        `json:"command"`
	Success  bool          `json:"success"`
	Output   string        `json:"output"`
	Error    string        `json:"error"`
	Duration time.Duration `json:"duration"`
	ExitCode int           `json:"exit_code"`
}

// ExecutionStatus 执行状态
type ExecutionStatus struct {
	ID          string     `json:"id"`
	Status      string     `json:"status"` // pending, running, completed, failed, cancelled
	Progress    float64    `json:"progress"`
	CurrentStep int        `json:"current_step"`
	TotalSteps  int        `json:"total_steps"`
	StartTime   time.Time  `json:"start_time"`
	EndTime     *time.Time `json:"end_time,omitempty"`
}

// NewUnifiedDispatcher 创建统一调度器
func NewUnifiedDispatcher(
	classifier IntentClassifier,
	inferenceEngine ParameterInferenceEngine,
	executionEngine ExecutionEngine,
	responseBuilder ResponseBuilder,
	logger *logrus.Logger,
) UnifiedDispatcher {
	config := &DispatcherConfig{
		MaxProcessingTime: 5 * time.Minute,
		EnableAsync:       true,
		RetryAttempts:     3,
		RetryDelay:        2 * time.Second,
	}

	return &unifiedDispatcher{
		classifier:       classifier,
		inferenceEngine:  inferenceEngine,
		executionEngine:  executionEngine,
		responseBuilder:  responseBuilder,
		logger:           logger,
		processingStatus: make(map[string]*ProcessingStatus),
		config:           config,
	}
}

// ProcessMessage 处理消息
func (ud *unifiedDispatcher) ProcessMessage(ctx context.Context, req *DualLayerRequest) (*DualLayerResponse, error) {
	start := time.Now()

	ud.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
	}).Info("Starting dual-layer message processing")

	// 创建处理状态
	status := &ProcessingStatus{
		SessionID:     req.SessionID,
		Stage:         "classification",
		Progress:      0.0,
		CurrentStep:   "intent_classification",
		EstimatedTime: 30,
		StartTime:     start,
		LastUpdate:    start,
	}
	ud.setProcessingStatus(req.SessionID, status)

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(ctx, ud.config.MaxProcessingTime)
	defer cancel()

	// 第一层：意图分类
	classification, err := ud.performClassification(ctx, req, status)
	if err != nil {
		ud.removeProcessingStatus(req.SessionID)
		return ud.responseBuilder.BuildErrorResponse(err, "classification")
	}

	// 第二层：参数推断
	inference, err := ud.performInference(ctx, req, classification, status)
	if err != nil {
		ud.removeProcessingStatus(req.SessionID)
		return ud.responseBuilder.BuildErrorResponse(err, "inference")
	}

	// 第三层：命令执行（如果需要）
	var execution *ExecutionResult
	if len(inference.Commands) > 0 {
		execution, err = ud.performExecution(ctx, inference, status)
		if err != nil {
			ud.logger.WithError(err).Warn("Command execution failed, but continuing with response")
			// 执行失败不阻断响应生成
		}
	}

	// 构建最终响应
	response, err := ud.responseBuilder.BuildResponse(ctx, req, classification, inference, execution)
	if err != nil {
		ud.removeProcessingStatus(req.SessionID)
		return ud.responseBuilder.BuildErrorResponse(err, "response_building")
	}

	// 设置处理时间
	response.ProcessingTime = time.Since(start)
	response.Timestamp = time.Now()

	// 清理处理状态
	ud.removeProcessingStatus(req.SessionID)

	ud.logger.WithFields(logrus.Fields{
		"session_id":      req.SessionID,
		"intent":          classification.Intent,
		"confidence":      classification.Confidence,
		"commands_count":  len(inference.Commands),
		"processing_time": response.ProcessingTime,
	}).Info("Dual-layer message processing completed")

	return response, nil
}

// performClassification 执行分类
func (ud *unifiedDispatcher) performClassification(ctx context.Context, req *DualLayerRequest, status *ProcessingStatus) (*ClassificationResult, error) {
	ud.updateProcessingStatus(req.SessionID, "classification", 0.1, "performing_intent_classification")

	classification, err := ud.classifier.ClassifyIntent(ctx, req.Message, req.Context)
	if err != nil {
		return nil, NewDualLayerError(ErrorCodeClassificationFailed, fmt.Sprintf("Intent classification failed: %v", err), "classification")
	}

	// 更新上下文
	if err := ud.classifier.UpdateContext(req.SessionID, classification); err != nil {
		ud.logger.WithError(err).Warn("Failed to update context after classification")
	}

	ud.updateProcessingStatus(req.SessionID, "classification", 0.3, "classification_completed")
	return classification, nil
}

// performInference 执行推断
func (ud *unifiedDispatcher) performInference(ctx context.Context, req *DualLayerRequest, classification *ClassificationResult, status *ProcessingStatus) (*InferenceResult, error) {
	ud.updateProcessingStatus(req.SessionID, "inference", 0.4, "performing_parameter_inference")

	inference, err := ud.inferenceEngine.InferParameters(ctx, classification, req.Message, req.Context)
	if err != nil {
		return nil, NewDualLayerError(ErrorCodeInferenceFailed, fmt.Sprintf("Parameter inference failed: %v", err), "inference")
	}

	ud.updateProcessingStatus(req.SessionID, "inference", 0.6, "inference_completed")
	return inference, nil
}

// performExecution 执行命令
func (ud *unifiedDispatcher) performExecution(ctx context.Context, inference *InferenceResult, status *ProcessingStatus) (*ExecutionResult, error) {
	ud.updateProcessingStatus(status.SessionID, "execution", 0.7, "executing_commands")

	execution, err := ud.executionEngine.ExecuteCommands(ctx, inference.Commands, inference.ExecutionPlan)
	if err != nil {
		return nil, NewDualLayerError(ErrorCodeExecutionFailed, fmt.Sprintf("Command execution failed: %v", err), "execution")
	}

	ud.updateProcessingStatus(status.SessionID, "execution", 0.9, "execution_completed")
	return execution, nil
}

// GetProcessingStatus 获取处理状态
func (ud *unifiedDispatcher) GetProcessingStatus(sessionID string) (*ProcessingStatus, error) {
	ud.statusMutex.RLock()
	defer ud.statusMutex.RUnlock()

	status, exists := ud.processingStatus[sessionID]
	if !exists {
		return nil, fmt.Errorf("no processing status found for session: %s", sessionID)
	}

	// 返回状态副本
	statusCopy := *status
	return &statusCopy, nil
}

// CancelProcessing 取消处理
func (ud *unifiedDispatcher) CancelProcessing(sessionID string) error {
	ud.statusMutex.Lock()
	defer ud.statusMutex.Unlock()

	status, exists := ud.processingStatus[sessionID]
	if !exists {
		return fmt.Errorf("no active processing found for session: %s", sessionID)
	}

	// 如果正在执行命令，尝试取消执行
	if status.Stage == "execution" {
		if err := ud.executionEngine.CancelExecution(sessionID); err != nil {
			ud.logger.WithError(err).Warn("Failed to cancel command execution")
		}
	}

	// 移除处理状态
	delete(ud.processingStatus, sessionID)

	ud.logger.WithField("session_id", sessionID).Info("Processing cancelled")
	return nil
}

// setProcessingStatus 设置处理状态
func (ud *unifiedDispatcher) setProcessingStatus(sessionID string, status *ProcessingStatus) {
	ud.statusMutex.Lock()
	defer ud.statusMutex.Unlock()
	ud.processingStatus[sessionID] = status
}

// updateProcessingStatus 更新处理状态
func (ud *unifiedDispatcher) updateProcessingStatus(sessionID, stage string, progress float64, currentStep string) {
	ud.statusMutex.Lock()
	defer ud.statusMutex.Unlock()

	if status, exists := ud.processingStatus[sessionID]; exists {
		status.Stage = stage
		status.Progress = progress
		status.CurrentStep = currentStep
		status.LastUpdate = time.Now()

		// 更新预估时间
		elapsed := time.Since(status.StartTime)
		if progress > 0 {
			totalEstimated := time.Duration(float64(elapsed) / progress)
			remaining := totalEstimated - elapsed
			status.EstimatedTime = int(remaining.Seconds())
		}
	}
}

// removeProcessingStatus 移除处理状态
func (ud *unifiedDispatcher) removeProcessingStatus(sessionID string) {
	ud.statusMutex.Lock()
	defer ud.statusMutex.Unlock()
	delete(ud.processingStatus, sessionID)
}
