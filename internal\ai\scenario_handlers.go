package ai

import (
	"context"
	"fmt"

	"aiops-platform/internal/workflow"

	"github.com/sirupsen/logrus"
)

// 基础场景处理器实现

// commandExecutionHandler 命令执行场景处理器
type commandExecutionHandler struct {
	deepseekService interface{} // 简化为interface{}
	logger          *logrus.Logger
}

func NewCommandExecutionHandler(deepseekService interface{}, logger *logrus.Logger) ScenarioHandler {
	return &commandExecutionHandler{
		deepseekService: deepseekService,
		logger:          logger,
	}
}

func (ceh *commandExecutionHandler) CanHandle(intent string, entities map[string]interface{}) bool {
	return intent == IntentCommandExecution
}

func (ceh *commandExecutionHandler) GenerateCommands(ctx context.Context, classification *ClassificationResult, message string) ([]CommandSequence, error) {
	// 从实体中提取命令
	if command, exists := classification.Entities[EntityCommand]; exists {
		if cmdStr, ok := command.(string); ok {
			return []CommandSequence{
				{
					Step:        1,
					Command:     cmdStr,
					Description: fmt.Sprintf("执行命令: %s", cmdStr),
					Timeout:     30,
					Required:    true,
					OnError:     "stop",
				},
			}, nil
		}
	}

	return []CommandSequence{}, fmt.Errorf("no command found in entities")
}

func (ceh *commandExecutionHandler) GenerateExecutionPlan(commands []CommandSequence) *ExecutionPlan {
	return &ExecutionPlan{
		Strategy: "sequential",
		Timeout:  60,
		// 移除不存在的字段
	}
}

func (ceh *commandExecutionHandler) GetDescription() string {
	return "Command execution handler for direct command execution"
}

// systemMonitoringHandler 系统监控场景处理器
type systemMonitoringHandler struct {
	deepseekService interface{} // 简化为interface{}
	logger          *logrus.Logger
}

func NewSystemMonitoringHandler(deepseekService interface{}, logger *logrus.Logger) ScenarioHandler {
	return &systemMonitoringHandler{
		deepseekService: deepseekService,
		logger:          logger,
	}
}

func (smh *systemMonitoringHandler) CanHandle(intent string, entities map[string]interface{}) bool {
	return intent == IntentSystemMonitoring
}

func (smh *systemMonitoringHandler) GenerateCommands(ctx context.Context, classification *ClassificationResult, message string) ([]CommandSequence, error) {
	return []CommandSequence{
		{
			Step:        1,
			Command:     "top -bn1 | head -20",
			Description: "查看CPU和内存使用情况",
			Timeout:     10,
			Required:    true,
			OnError:     "continue",
		},
		{
			Step:        2,
			Command:     "df -h",
			Description: "查看磁盘使用情况",
			Timeout:     10,
			Required:    true,
			OnError:     "continue",
		},
		{
			Step:        3,
			Command:     "free -h",
			Description: "查看内存详细信息",
			Timeout:     10,
			Required:    false,
			OnError:     "continue",
		},
	}, nil
}

func (smh *systemMonitoringHandler) GenerateExecutionPlan(commands []CommandSequence) *ExecutionPlan {
	return &ExecutionPlan{
		Strategy: "sequential",
		Timeout:  45,
		// 移除不存在的字段
	}
}

func (smh *systemMonitoringHandler) GetDescription() string {
	return "System monitoring handler for CPU, memory, and disk monitoring"
}

// networkDiagnosisHandler 网络诊断场景处理器
type networkDiagnosisHandler struct {
	deepseekService interface{} // 简化为interface{}
	logger          *logrus.Logger
}

func NewNetworkDiagnosisHandler(deepseekService interface{}, logger *logrus.Logger) ScenarioHandler {
	return &networkDiagnosisHandler{
		deepseekService: deepseekService,
		logger:          logger,
	}
}

func (ndh *networkDiagnosisHandler) CanHandle(intent string, entities map[string]interface{}) bool {
	return intent == IntentNetworkDiagnosis
}

func (ndh *networkDiagnosisHandler) GenerateCommands(ctx context.Context, classification *ClassificationResult, message string) ([]CommandSequence, error) {
	targetHost := "localhost"
	if ip, exists := classification.Entities[EntityIPAddress]; exists {
		if ipStr, ok := ip.(string); ok {
			targetHost = ipStr
		}
	}

	return []CommandSequence{
		{
			Step:        1,
			Command:     fmt.Sprintf("ping -c 4 %s", targetHost),
			Description: fmt.Sprintf("测试到 %s 的网络连通性", targetHost),
			Timeout:     15,
			Required:    true,
			OnError:     "continue",
		},
		{
			Step:        2,
			Command:     "netstat -tuln",
			Description: "查看网络连接状态",
			Timeout:     10,
			Required:    false,
			OnError:     "continue",
		},
	}, nil
}

func (ndh *networkDiagnosisHandler) GenerateExecutionPlan(commands []CommandSequence) *ExecutionPlan {
	return &ExecutionPlan{
		Strategy: "sequential",
		Timeout:  30,
		// 移除不存在的字段
	}
}

func (ndh *networkDiagnosisHandler) GetDescription() string {
	return "Network diagnosis handler for connectivity and network status checks"
}

// hostManagementHandler 主机管理场景处理器
type hostManagementHandler struct {
	deepseekService interface{} // 简化为interface{}
	hostService     workflow.HostServiceInterface
	logger          *logrus.Logger
}

// 删除重复的构造函数，使用intent_handlers.go中的版本

func (hmh *hostManagementHandler) CanHandle(intent string, entities map[string]interface{}) bool {
	return intent == IntentHostManagement
}

func (hmh *hostManagementHandler) GenerateCommands(ctx context.Context, classification *ClassificationResult, message string) ([]CommandSequence, error) {
	// 主机管理通常不需要生成命令，而是直接调用API
	return []CommandSequence{}, nil
}

func (hmh *hostManagementHandler) GenerateExecutionPlan(commands []CommandSequence) *ExecutionPlan {
	return &ExecutionPlan{
		Strategy: "sequential",
		Timeout:  30,
		// 移除不存在的字段
	}
}

func (hmh *hostManagementHandler) GetDescription() string {
	return "Host management handler for adding, removing, and listing hosts"
}

// 其他简单的处理器实现

func NewServiceManagementHandler(deepseekService interface{}, logger *logrus.Logger) ScenarioHandler {
	return &basicHandler{intent: IntentServiceManagement, description: "Service management handler"}
}

func NewLogAnalysisHandler(deepseekService interface{}, logger *logrus.Logger) ScenarioHandler {
	return &basicHandler{intent: IntentLogAnalysis, description: "Log analysis handler"}
}

func NewFileOperationsHandler(deepseekService interface{}, logger *logrus.Logger) ScenarioHandler {
	return &basicHandler{intent: IntentFileOperations, description: "File operations handler"}
}

func NewSecurityCheckHandler(deepseekService interface{}, logger *logrus.Logger) ScenarioHandler {
	return &basicHandler{intent: IntentSecurityCheck, description: "Security check handler"}
}

func NewPerformanceAnalysisHandler(deepseekService interface{}, logger *logrus.Logger) ScenarioHandler {
	return &basicHandler{intent: IntentPerformanceAnalysis, description: "Performance analysis handler"}
}

func NewGeneralChatHandler(deepseekService interface{}, logger *logrus.Logger) ScenarioHandler {
	return &basicHandler{intent: IntentGeneralChat, description: "General chat handler"}
}

// basicHandler 基础处理器实现
type basicHandler struct {
	intent      string
	description string
}

func (bh *basicHandler) CanHandle(intent string, entities map[string]interface{}) bool {
	return intent == bh.intent
}

func (bh *basicHandler) GenerateCommands(ctx context.Context, classification *ClassificationResult, message string) ([]CommandSequence, error) {
	return []CommandSequence{}, nil
}

func (bh *basicHandler) GenerateExecutionPlan(commands []CommandSequence) *ExecutionPlan {
	return &ExecutionPlan{
		Strategy: "sequential",
		Timeout:  30,
		// 移除不存在的字段
	}
}

func (bh *basicHandler) GetDescription() string {
	return bh.description
}
