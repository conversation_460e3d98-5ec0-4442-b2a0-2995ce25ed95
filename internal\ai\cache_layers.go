package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// MemoryCache L1内存缓存
type MemoryCache struct {
	items     map[string]*CacheItem
	maxSize   int64
	currentSize int64
	ttl       time.Duration
	mutex     sync.RWMutex
	stats     *LayerStats
}

// PersistentCache L2持久化缓存
type PersistentCache struct {
	basePath    string
	maxSize     int64
	currentSize int64
	ttl         time.Duration
	index       map[string]*CacheIndex
	mutex       sync.RWMutex
	stats       *LayerStats
}

// DistributedCache L3分布式缓存
type DistributedCache struct {
	nodes       []string
	ttl         time.Duration
	replication int
	mutex       sync.RWMutex
	stats       *LayerStats
}

// CacheIndex 缓存索引
type CacheIndex struct {
	Key       string    `json:"key"`
	FilePath  string    `json:"file_path"`
	Size      int64     `json:"size"`
	CreatedAt time.Time `json:"created_at"`
	ExpiresAt time.Time `json:"expires_at"`
	Tags      []string  `json:"tags"`
}

// NewMemoryCache 创建内存缓存
func NewMemoryCache(maxSize int64, ttl time.Duration) *MemoryCache {
	return &MemoryCache{
		items:       make(map[string]*CacheItem),
		maxSize:     maxSize,
		currentSize: 0,
		ttl:         ttl,
		stats:       &LayerStats{},
	}
}

// Get 获取缓存项
func (mc *MemoryCache) Get(key string) (*CacheItem, bool) {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	item, exists := mc.items[key]
	if !exists {
		mc.stats.Misses++
		return nil, false
	}

	// 检查是否过期
	if time.Now().After(item.ExpiresAt) {
		mc.mutex.RUnlock()
		mc.mutex.Lock()
		delete(mc.items, key)
		mc.currentSize -= item.Size
		mc.mutex.Unlock()
		mc.mutex.RLock()
		mc.stats.Misses++
		return nil, false
	}

	mc.stats.Hits++
	return item, true
}

// Set 设置缓存项
func (mc *MemoryCache) Set(key string, item *CacheItem) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	// 检查是否需要驱逐
	if mc.currentSize+item.Size > mc.maxSize {
		mc.evictLRU(item.Size)
	}

	// 如果键已存在，先删除旧项
	if oldItem, exists := mc.items[key]; exists {
		mc.currentSize -= oldItem.Size
	}

	mc.items[key] = item
	mc.currentSize += item.Size
}

// Delete 删除缓存项
func (mc *MemoryCache) Delete(key string) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	if item, exists := mc.items[key]; exists {
		delete(mc.items, key)
		mc.currentSize -= item.Size
	}
}

// DeleteByTags 根据标签删除
func (mc *MemoryCache) DeleteByTags(tags []string) int {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	deletedCount := 0
	for key, item := range mc.items {
		if mc.hasAnyTag(item.Tags, tags) {
			delete(mc.items, key)
			mc.currentSize -= item.Size
			deletedCount++
		}
	}

	return deletedCount
}

// Cleanup 清理过期项
func (mc *MemoryCache) Cleanup() {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	now := time.Now()
	for key, item := range mc.items {
		if now.After(item.ExpiresAt) {
			delete(mc.items, key)
			mc.currentSize -= item.Size
		}
	}
}

// GetStats 获取统计信息
func (mc *MemoryCache) GetStats() *LayerStats {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	mc.stats.Size = mc.currentSize
	mc.stats.ItemCount = int64(len(mc.items))
	if mc.stats.Hits+mc.stats.Misses > 0 {
		mc.stats.HitRatio = float64(mc.stats.Hits) / float64(mc.stats.Hits+mc.stats.Misses)
	}

	return mc.stats
}

// evictLRU LRU驱逐策略
func (mc *MemoryCache) evictLRU(neededSize int64) {
	// 找到最久未访问的项
	var oldestKey string
	var oldestTime time.Time = time.Now()

	for key, item := range mc.items {
		if item.AccessedAt.Before(oldestTime) {
			oldestTime = item.AccessedAt
			oldestKey = key
		}
	}

	// 驱逐最旧的项
	if oldestKey != "" {
		if item, exists := mc.items[oldestKey]; exists {
			delete(mc.items, oldestKey)
			mc.currentSize -= item.Size
			mc.stats.Evictions++
		}
	}

	// 如果还需要更多空间，继续驱逐
	if mc.currentSize+neededSize > mc.maxSize && len(mc.items) > 0 {
		mc.evictLRU(neededSize)
	}
}

func (mc *MemoryCache) hasAnyTag(itemTags, searchTags []string) bool {
	for _, searchTag := range searchTags {
		for _, itemTag := range itemTags {
			if itemTag == searchTag {
				return true
			}
		}
	}
	return false
}

// NewPersistentCache 创建持久化缓存
func NewPersistentCache(maxSize int64, ttl time.Duration) *PersistentCache {
	basePath := "./cache/l2"
	os.MkdirAll(basePath, 0755)

	pc := &PersistentCache{
		basePath:    basePath,
		maxSize:     maxSize,
		currentSize: 0,
		ttl:         ttl,
		index:       make(map[string]*CacheIndex),
		stats:       &LayerStats{},
	}

	// 加载现有索引
	pc.loadIndex()

	return pc
}

// Get 获取缓存项
func (pc *PersistentCache) Get(key string) (*CacheItem, bool) {
	pc.mutex.RLock()
	defer pc.mutex.RUnlock()

	indexItem, exists := pc.index[key]
	if !exists {
		pc.stats.Misses++
		return nil, false
	}

	// 检查是否过期
	if time.Now().After(indexItem.ExpiresAt) {
		pc.mutex.RUnlock()
		pc.mutex.Lock()
		pc.removeFromIndex(key)
		pc.mutex.Unlock()
		pc.mutex.RLock()
		pc.stats.Misses++
		return nil, false
	}

	// 从文件加载
	item, err := pc.loadFromFile(indexItem.FilePath)
	if err != nil {
		pc.stats.Errors++
		pc.stats.Misses++
		return nil, false
	}

	pc.stats.Hits++
	return item, true
}

// Set 设置缓存项
func (pc *PersistentCache) Set(key string, item *CacheItem) {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()

	// 检查是否需要驱逐
	if pc.currentSize+item.Size > pc.maxSize {
		pc.evictOldest(item.Size)
	}

	// 保存到文件
	filePath := pc.generateFilePath(key)
	if err := pc.saveToFile(filePath, item); err != nil {
		pc.stats.Errors++
		return
	}

	// 更新索引
	indexItem := &CacheIndex{
		Key:       key,
		FilePath:  filePath,
		Size:      item.Size,
		CreatedAt: item.CreatedAt,
		ExpiresAt: item.ExpiresAt,
		Tags:      item.Tags,
	}

	// 如果键已存在，先删除旧项
	if oldIndex, exists := pc.index[key]; exists {
		pc.currentSize -= oldIndex.Size
		os.Remove(oldIndex.FilePath)
	}

	pc.index[key] = indexItem
	pc.currentSize += item.Size

	// 保存索引
	pc.saveIndex()
}

// Delete 删除缓存项
func (pc *PersistentCache) Delete(key string) {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()

	pc.removeFromIndex(key)
	pc.saveIndex()
}

// DeleteByTags 根据标签删除
func (pc *PersistentCache) DeleteByTags(tags []string) int {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()

	deletedCount := 0
	for key, indexItem := range pc.index {
		if pc.hasAnyTag(indexItem.Tags, tags) {
			pc.removeFromIndex(key)
			deletedCount++
		}
	}

	pc.saveIndex()
	return deletedCount
}

// Cleanup 清理过期项
func (pc *PersistentCache) Cleanup() {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()

	now := time.Now()
	for key, indexItem := range pc.index {
		if now.After(indexItem.ExpiresAt) {
			pc.removeFromIndex(key)
		}
	}

	pc.saveIndex()
}

// GetStats 获取统计信息
func (pc *PersistentCache) GetStats() *LayerStats {
	pc.mutex.RLock()
	defer pc.mutex.RUnlock()

	pc.stats.Size = pc.currentSize
	pc.stats.ItemCount = int64(len(pc.index))
	if pc.stats.Hits+pc.stats.Misses > 0 {
		pc.stats.HitRatio = float64(pc.stats.Hits) / float64(pc.stats.Hits+pc.stats.Misses)
	}

	return pc.stats
}

// 私有方法

func (pc *PersistentCache) generateFilePath(key string) string {
	return filepath.Join(pc.basePath, fmt.Sprintf("%x.cache", []byte(key)))
}

func (pc *PersistentCache) saveToFile(filePath string, item *CacheItem) error {
	data, err := json.Marshal(item)
	if err != nil {
		return err
	}

	return os.WriteFile(filePath, data, 0644)
}

func (pc *PersistentCache) loadFromFile(filePath string) (*CacheItem, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	var item CacheItem
	if err := json.Unmarshal(data, &item); err != nil {
		return nil, err
	}

	return &item, nil
}

func (pc *PersistentCache) removeFromIndex(key string) {
	if indexItem, exists := pc.index[key]; exists {
		pc.currentSize -= indexItem.Size
		os.Remove(indexItem.FilePath)
		delete(pc.index, key)
	}
}

func (pc *PersistentCache) evictOldest(neededSize int64) {
	// 找到最旧的项
	var oldestKey string
	var oldestTime time.Time = time.Now()

	for key, indexItem := range pc.index {
		if indexItem.CreatedAt.Before(oldestTime) {
			oldestTime = indexItem.CreatedAt
			oldestKey = key
		}
	}

	// 驱逐最旧的项
	if oldestKey != "" {
		pc.removeFromIndex(oldestKey)
		pc.stats.Evictions++
	}

	// 如果还需要更多空间，继续驱逐
	if pc.currentSize+neededSize > pc.maxSize && len(pc.index) > 0 {
		pc.evictOldest(neededSize)
	}
}

func (pc *PersistentCache) loadIndex() {
	indexPath := filepath.Join(pc.basePath, "index.json")
	data, err := os.ReadFile(indexPath)
	if err != nil {
		return // 索引文件不存在，使用空索引
	}

	json.Unmarshal(data, &pc.index)

	// 计算当前大小
	for _, indexItem := range pc.index {
		pc.currentSize += indexItem.Size
	}
}

func (pc *PersistentCache) saveIndex() {
	indexPath := filepath.Join(pc.basePath, "index.json")
	data, _ := json.MarshalIndent(pc.index, "", "  ")
	os.WriteFile(indexPath, data, 0644)
}

func (pc *PersistentCache) hasAnyTag(itemTags, searchTags []string) bool {
	for _, searchTag := range searchTags {
		for _, itemTag := range itemTags {
			if itemTag == searchTag {
				return true
			}
		}
	}
	return false
}

// NewDistributedCache 创建分布式缓存
func NewDistributedCache(ttl time.Duration) *DistributedCache {
	return &DistributedCache{
		nodes:       []string{"localhost:6379"}, // 默认Redis节点
		ttl:         ttl,
		replication: 1,
		stats:       &LayerStats{},
	}
}

// Get 获取缓存项
func (dc *DistributedCache) Get(ctx context.Context, key string) (*CacheItem, bool) {
	// 这里应该实现Redis或其他分布式缓存的获取逻辑
	// 为了简化，暂时返回false
	dc.stats.Misses++
	return nil, false
}

// Set 设置缓存项
func (dc *DistributedCache) Set(ctx context.Context, key string, item *CacheItem) error {
	// 这里应该实现Redis或其他分布式缓存的设置逻辑
	// 为了简化，暂时返回nil
	return nil
}

// Delete 删除缓存项
func (dc *DistributedCache) Delete(ctx context.Context, key string) error {
	// 这里应该实现Redis或其他分布式缓存的删除逻辑
	return nil
}

// DeleteByTags 根据标签删除
func (dc *DistributedCache) DeleteByTags(ctx context.Context, tags []string) error {
	// 这里应该实现Redis或其他分布式缓存的标签删除逻辑
	return nil
}

// GetStats 获取统计信息
func (dc *DistributedCache) GetStats() *LayerStats {
	return dc.stats
}
