package ai

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aiops-platform/internal/agent"
	"github.com/sirupsen/logrus"
)

// SmartExecutionCoordinator 智能执行协调器
type SmartExecutionCoordinator struct {
	agentRegistry   *agent.AgentRegistry
	executionEngine *agent.ExecutionEngine
	logger          *logrus.Logger
	config          *CoordinatorConfig
	
	// 执行状态管理
	activeSessions map[string]*SmartExecutionSession
	mutex          sync.RWMutex
}

// CoordinatorConfig 协调器配置
type CoordinatorConfig struct {
	MaxConcurrentSessions int           `json:"max_concurrent_sessions"`
	DefaultTimeout        time.Duration `json:"default_timeout"`
	EnableRetry           bool          `json:"enable_retry"`
	MaxRetryAttempts      int           `json:"max_retry_attempts"`
	RetryInterval         time.Duration `json:"retry_interval"`
}

// SmartExecutionSession 智能执行会话
type SmartExecutionSession struct {
	SessionID       string                 `json:"session_id"`
	UserID          int64                  `json:"user_id"`
	DispatchResult  *AgentDispatchResult   `json:"dispatch_result"`
	Status          ExecutionStatus        `json:"status"`
	StartTime       time.Time              `json:"start_time"`
	EndTime         *time.Time             `json:"end_time,omitempty"`
	Results         []*StepExecutionResult `json:"results"`
	CurrentStep     int                    `json:"current_step"`
	TotalSteps      int                    `json:"total_steps"`
	ErrorMessage    string                 `json:"error_message,omitempty"`
	Metadata        map[string]interface{} `json:"metadata"`
	
	// 执行控制
	ctx    context.Context
	cancel context.CancelFunc
}

// ExecutionStatus 执行状态
type ExecutionStatus string

const (
	StatusPending    ExecutionStatus = "pending"
	StatusRunning    ExecutionStatus = "running"
	StatusCompleted  ExecutionStatus = "completed"
	StatusFailed     ExecutionStatus = "failed"
	StatusCancelled  ExecutionStatus = "cancelled"
	StatusTimeout    ExecutionStatus = "timeout"
)

// StepExecutionResult 步骤执行结果
type StepExecutionResult struct {
	StepID       string                 `json:"step_id"`
	AgentID      string                 `json:"agent_id"`
	Capability   string                 `json:"capability"`
	Status       ExecutionStatus        `json:"status"`
	StartTime    time.Time              `json:"start_time"`
	EndTime      *time.Time             `json:"end_time,omitempty"`
	Duration     time.Duration          `json:"duration"`
	Success      bool                   `json:"success"`
	Data         map[string]interface{} `json:"data,omitempty"`
	ErrorMessage string                 `json:"error_message,omitempty"`
	RetryCount   int                    `json:"retry_count"`
}

// NewSmartExecutionCoordinator 创建智能执行协调器
func NewSmartExecutionCoordinator(
	agentRegistry *agent.AgentRegistry,
	executionEngine *agent.ExecutionEngine,
	logger *logrus.Logger,
) *SmartExecutionCoordinator {
	config := &CoordinatorConfig{
		MaxConcurrentSessions: 10,
		DefaultTimeout:        5 * time.Minute,
		EnableRetry:           true,
		MaxRetryAttempts:      3,
		RetryInterval:         5 * time.Second,
	}

	return &SmartExecutionCoordinator{
		agentRegistry:   agentRegistry,
		executionEngine: executionEngine,
		logger:          logger,
		config:          config,
		activeSessions:  make(map[string]*SmartExecutionSession),
	}
}

// ExecuteDispatchResult 执行调度结果
func (sec *SmartExecutionCoordinator) ExecuteDispatchResult(
	ctx context.Context,
	sessionID string,
	userID int64,
	dispatchResult *AgentDispatchResult,
) (*SmartExecutionSession, error) {
	sec.mutex.Lock()
	defer sec.mutex.Unlock()

	// 检查并发限制
	if len(sec.activeSessions) >= sec.config.MaxConcurrentSessions {
		return nil, fmt.Errorf("maximum concurrent sessions reached: %d", sec.config.MaxConcurrentSessions)
	}

	// 创建执行上下文
	execCtx, cancel := context.WithTimeout(ctx, sec.config.DefaultTimeout)

	// 创建执行会话
	session := &SmartExecutionSession{
		SessionID:      sessionID,
		UserID:         userID,
		DispatchResult: dispatchResult,
		Status:         StatusPending,
		StartTime:      time.Now(),
		Results:        make([]*StepExecutionResult, 0),
		CurrentStep:    0,
		TotalSteps:     len(dispatchResult.ExecutionPlan.Steps),
		Metadata:       make(map[string]interface{}),
		ctx:            execCtx,
		cancel:         cancel,
	}

	// 注册会话
	sec.activeSessions[sessionID] = session

	sec.logger.WithFields(logrus.Fields{
		"session_id":      sessionID,
		"user_id":         userID,
		"selected_agents": len(dispatchResult.SelectedAgents),
		"strategy":        dispatchResult.ExecutionPlan.Strategy,
		"total_steps":     session.TotalSteps,
	}).Info("Starting smart execution session")

	// 异步执行
	go sec.executeSession(session)

	return session, nil
}

// executeSession 执行会话
func (sec *SmartExecutionCoordinator) executeSession(session *SmartExecutionSession) {
	defer func() {
		sec.mutex.Lock()
		delete(sec.activeSessions, session.SessionID)
		sec.mutex.Unlock()

		if session.cancel != nil {
			session.cancel()
		}
	}()

	session.Status = StatusRunning

	sec.logger.WithField("session_id", session.SessionID).Info("Executing smart session")

	var err error
	switch session.DispatchResult.ExecutionPlan.Strategy {
	case "sequential":
		err = sec.executeSequential(session)
	case "parallel":
		err = sec.executeParallel(session)
	case "conditional":
		err = sec.executeConditional(session)
	case "pipeline":
		err = sec.executePipeline(session)
	default:
		err = fmt.Errorf("unsupported execution strategy: %s", session.DispatchResult.ExecutionPlan.Strategy)
	}

	// 设置最终状态
	now := time.Now()
	session.EndTime = &now

	if err != nil {
		session.Status = StatusFailed
		session.ErrorMessage = err.Error()
		sec.logger.WithError(err).WithField("session_id", session.SessionID).Error("Session execution failed")
	} else {
		session.Status = StatusCompleted
		sec.logger.WithField("session_id", session.SessionID).Info("Session execution completed successfully")
	}
}

// executeSequential 顺序执行
func (sec *SmartExecutionCoordinator) executeSequential(session *SmartExecutionSession) error {
	for i, step := range session.DispatchResult.ExecutionPlan.Steps {
		session.CurrentStep = i + 1

		result, err := sec.executeStep(session, step)
		session.Results = append(session.Results, result)

		if err != nil {
			return fmt.Errorf("step %d failed: %w", i+1, err)
		}

		if !result.Success {
			// 检查失败处理策略
			if step.OnFailure == "stop" || step.OnFailure == "" {
				return fmt.Errorf("step %d failed: %s", i+1, result.ErrorMessage)
			}
			// 如果是continue，继续执行下一步
		}

		// 检查是否被取消
		select {
		case <-session.ctx.Done():
			return session.ctx.Err()
		default:
		}
	}

	return nil
}

// executeParallel 并行执行
func (sec *SmartExecutionCoordinator) executeParallel(session *SmartExecutionSession) error {
	steps := session.DispatchResult.ExecutionPlan.Steps
	results := make([]*StepExecutionResult, len(steps))
	errors := make([]error, len(steps))

	var wg sync.WaitGroup
	for i, step := range steps {
		wg.Add(1)
		go func(index int, s *ExecutionStep) {
			defer wg.Done()
			results[index], errors[index] = sec.executeStep(session, s)
		}(i, step)
	}

	wg.Wait()

	// 收集结果
	session.Results = append(session.Results, results...)

	// 检查是否有错误
	for i, err := range errors {
		if err != nil {
			return fmt.Errorf("parallel step %d failed: %w", i+1, err)
		}
	}

	return nil
}

// executeConditional 条件执行
func (sec *SmartExecutionCoordinator) executeConditional(session *SmartExecutionSession) error {
	for i, step := range session.DispatchResult.ExecutionPlan.Steps {
		// 评估条件
		if step.Condition != "" {
			shouldExecute, err := sec.evaluateCondition(session, step.Condition)
			if err != nil {
				return fmt.Errorf("failed to evaluate condition for step %d: %w", i+1, err)
			}
			if !shouldExecute {
				sec.logger.WithFields(logrus.Fields{
					"session_id": session.SessionID,
					"step_id":    step.StepID,
					"condition":  step.Condition,
				}).Info("Skipping step due to condition")
				continue
			}
		}

		session.CurrentStep = i + 1
		result, err := sec.executeStep(session, step)
		session.Results = append(session.Results, result)

		if err != nil {
			return fmt.Errorf("conditional step %d failed: %w", i+1, err)
		}

		// 检查是否被取消
		select {
		case <-session.ctx.Done():
			return session.ctx.Err()
		default:
		}
	}

	return nil
}

// executePipeline 管道执行
func (sec *SmartExecutionCoordinator) executePipeline(session *SmartExecutionSession) error {
	var previousResult *StepExecutionResult

	for i, step := range session.DispatchResult.ExecutionPlan.Steps {
		session.CurrentStep = i + 1

		// 如果有前一步的结果，将其作为输入
		if previousResult != nil && previousResult.Success {
			// 将前一步的输出作为当前步的输入
			if step.Parameters == nil {
				step.Parameters = make(map[string]interface{})
			}
			step.Parameters["previous_result"] = previousResult.Data
		}

		result, err := sec.executeStep(session, step)
		session.Results = append(session.Results, result)
		previousResult = result

		if err != nil {
			return fmt.Errorf("pipeline step %d failed: %w", i+1, err)
		}

		if !result.Success {
			return fmt.Errorf("pipeline step %d failed: %s", i+1, result.ErrorMessage)
		}

		// 检查是否被取消
		select {
		case <-session.ctx.Done():
			return session.ctx.Err()
		default:
		}
	}

	return nil
}

// executeStep 执行单个步骤
func (sec *SmartExecutionCoordinator) executeStep(session *SmartExecutionSession, step *ExecutionStep) (*StepExecutionResult, error) {
	startTime := time.Now()

	result := &StepExecutionResult{
		StepID:     step.StepID,
		AgentID:    step.AgentID,
		Capability: step.Capability,
		Status:     StatusRunning,
		StartTime:  startTime,
	}

	sec.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"step_id":    step.StepID,
		"agent_id":   step.AgentID,
		"capability": step.Capability,
	}).Info("Executing step")

	// 创建Agent执行请求
	execRequest := &agent.ExecutionRequest{
		ID:         fmt.Sprintf("%s_%s", session.SessionID, step.StepID),
		AgentID:    step.AgentID,
		Capability: step.Capability,
		Parameters: step.Parameters,
		Context: &agent.ExecutionContext{
			SessionID: session.SessionID,
			UserID:    session.UserID,
			TraceID:   session.SessionID,
			Timeout:   step.Timeout,
		},
	}

	// 执行Agent
	var execResult *agent.ExecutionResult
	var err error

	for attempt := 0; attempt <= sec.config.MaxRetryAttempts; attempt++ {
		if attempt > 0 {
			sec.logger.WithFields(logrus.Fields{
				"session_id": session.SessionID,
				"step_id":    step.StepID,
				"attempt":    attempt,
			}).Info("Retrying step execution")
			
			time.Sleep(sec.config.RetryInterval)
		}

		execResult, err = sec.executionEngine.ExecuteAgent(session.ctx, execRequest)
		if err == nil && execResult.Success {
			break
		}

		result.RetryCount = attempt
		
		if !sec.config.EnableRetry || attempt >= sec.config.MaxRetryAttempts {
			break
		}
	}

	// 设置结果
	endTime := time.Now()
	result.EndTime = &endTime
	result.Duration = endTime.Sub(startTime)

	if err != nil {
		result.Status = StatusFailed
		result.Success = false
		result.ErrorMessage = err.Error()
		return result, err
	}

	if execResult.Success {
		result.Status = StatusCompleted
		result.Success = true
		result.Data = execResult.Data
	} else {
		result.Status = StatusFailed
		result.Success = false
		result.ErrorMessage = execResult.ErrorMessage
	}

	return result, nil
}

// evaluateCondition 评估条件
func (sec *SmartExecutionCoordinator) evaluateCondition(session *SmartExecutionSession, condition string) (bool, error) {
	// 简单的条件评估实现
	// 在实际应用中，这里可以实现更复杂的条件评估逻辑
	
	switch condition {
	case "always":
		return true, nil
	case "never":
		return false, nil
	case "previous_success":
		if len(session.Results) > 0 {
			lastResult := session.Results[len(session.Results)-1]
			return lastResult.Success, nil
		}
		return true, nil
	case "previous_failed":
		if len(session.Results) > 0 {
			lastResult := session.Results[len(session.Results)-1]
			return !lastResult.Success, nil
		}
		return false, nil
	default:
		// 默认执行
		return true, nil
	}
}

// GetSession 获取执行会话
func (sec *SmartExecutionCoordinator) GetSession(sessionID string) (*SmartExecutionSession, bool) {
	sec.mutex.RLock()
	defer sec.mutex.RUnlock()
	
	session, exists := sec.activeSessions[sessionID]
	return session, exists
}

// CancelSession 取消执行会话
func (sec *SmartExecutionCoordinator) CancelSession(sessionID string) error {
	sec.mutex.Lock()
	defer sec.mutex.Unlock()

	session, exists := sec.activeSessions[sessionID]
	if !exists {
		return fmt.Errorf("session not found: %s", sessionID)
	}

	if session.cancel != nil {
		session.cancel()
	}

	session.Status = StatusCancelled
	now := time.Now()
	session.EndTime = &now

	sec.logger.WithField("session_id", sessionID).Info("Session cancelled")

	return nil
}

// GetActiveSessions 获取活跃会话列表
func (sec *SmartExecutionCoordinator) GetActiveSessions() map[string]*SmartExecutionSession {
	sec.mutex.RLock()
	defer sec.mutex.RUnlock()

	sessions := make(map[string]*SmartExecutionSession)
	for id, session := range sec.activeSessions {
		sessions[id] = session
	}

	return sessions
}
