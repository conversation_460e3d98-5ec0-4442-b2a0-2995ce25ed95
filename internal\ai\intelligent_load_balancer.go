package ai

import (
	"context"
	"fmt"
	"math"
	"sort"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// IntelligentLoadBalancer 智能负载均衡器
type IntelligentLoadBalancer struct {
	agents          map[string]*AgentNode
	strategies      map[string]LoadBalanceStrategy
	currentStrategy string
	healthChecker   *HealthChecker
	metrics         *LoadBalancerMetrics
	config          *LoadBalancerConfig
	logger          *logrus.Logger
	mutex           sync.RWMutex
}

// AgentNode Agent节点
type AgentNode struct {
	ID              string                 `json:"id"`
	Name            string                 `json:"name"`
	Endpoint        string                 `json:"endpoint"`
	Capabilities    []string               `json:"capabilities"`
	Status          NodeStatus             `json:"status"`
	Health          *HealthStatus          `json:"health"`
	Load            *LoadMetrics           `json:"load"`
	Performance     *PerformanceMetrics    `json:"performance"`
	Weight          int                    `json:"weight"`
	Priority        int                    `json:"priority"`
	Tags            []string               `json:"tags"`
	Metadata        map[string]interface{} `json:"metadata"`
	LastSeen        time.Time              `json:"last_seen"`
	CreatedAt       time.Time              `json:"created_at"`
}

// NodeStatus 节点状态
type NodeStatus string

const (
	NodeStatusHealthy     NodeStatus = "healthy"
	NodeStatusUnhealthy   NodeStatus = "unhealthy"
	NodeStatusMaintenance NodeStatus = "maintenance"
	NodeStatusDraining    NodeStatus = "draining"
	NodeStatusOffline     NodeStatus = "offline"
)

// HealthStatus 健康状态
type HealthStatus struct {
	IsHealthy        bool      `json:"is_healthy"`
	LastCheck        time.Time `json:"last_check"`
	ResponseTime     float64   `json:"response_time_ms"`
	ErrorCount       int       `json:"error_count"`
	ConsecutiveErrors int      `json:"consecutive_errors"`
	HealthScore      float64   `json:"health_score"`
}

// LoadMetrics 负载指标
type LoadMetrics struct {
	ActiveRequests   int     `json:"active_requests"`
	QueuedRequests   int     `json:"queued_requests"`
	CPUUsage         float64 `json:"cpu_usage"`
	MemoryUsage      float64 `json:"memory_usage"`
	NetworkIO        float64 `json:"network_io"`
	DiskIO           float64 `json:"disk_io"`
	LoadScore        float64 `json:"load_score"`
}

// LoadBalanceStrategy 负载均衡策略接口
type LoadBalanceStrategy interface {
	SelectAgent(ctx context.Context, agents []*AgentNode, request *LoadBalanceRequest) (*AgentNode, error)
	GetName() string
	GetDescription() string
}

// LoadBalanceRequest 负载均衡请求
type LoadBalanceRequest struct {
	RequestID       string                 `json:"request_id"`
	RequiredCapabilities []string          `json:"required_capabilities"`
	PreferredTags   []string               `json:"preferred_tags"`
	Priority        int                    `json:"priority"`
	Timeout         time.Duration          `json:"timeout"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// LoadBalancerMetrics 负载均衡器指标
type LoadBalancerMetrics struct {
	TotalRequests       int64                    `json:"total_requests"`
	SuccessfulRequests  int64                    `json:"successful_requests"`
	FailedRequests      int64                    `json:"failed_requests"`
	AvgResponseTime     float64                  `json:"avg_response_time_ms"`
	AgentDistribution   map[string]int64         `json:"agent_distribution"`
	StrategyUsage       map[string]int64         `json:"strategy_usage"`
	HealthyAgents       int                      `json:"healthy_agents"`
	UnhealthyAgents     int                      `json:"unhealthy_agents"`
	LastUpdate          time.Time                `json:"last_update"`
}

// LoadBalancerConfig 负载均衡器配置
type LoadBalancerConfig struct {
	DefaultStrategy      string        `json:"default_strategy"`
	HealthCheckInterval  time.Duration `json:"health_check_interval"`
	HealthCheckTimeout   time.Duration `json:"health_check_timeout"`
	MaxRetries           int           `json:"max_retries"`
	RetryInterval        time.Duration `json:"retry_interval"`
	EnableMetrics        bool          `json:"enable_metrics"`
	EnableAutoScaling    bool          `json:"enable_auto_scaling"`
	LoadThreshold        float64       `json:"load_threshold"`
}

// NewIntelligentLoadBalancer 创建智能负载均衡器
func NewIntelligentLoadBalancer(config *LoadBalancerConfig, logger *logrus.Logger) *IntelligentLoadBalancer {
	if config == nil {
		config = &LoadBalancerConfig{
			DefaultStrategy:     "weighted_round_robin",
			HealthCheckInterval: 30 * time.Second,
			HealthCheckTimeout:  5 * time.Second,
			MaxRetries:          3,
			RetryInterval:       1 * time.Second,
			EnableMetrics:       true,
			EnableAutoScaling:   false,
			LoadThreshold:       80.0,
		}
	}

	ilb := &IntelligentLoadBalancer{
		agents:          make(map[string]*AgentNode),
		strategies:      make(map[string]LoadBalanceStrategy),
		currentStrategy: config.DefaultStrategy,
		healthChecker:   NewHealthChecker(config.HealthCheckInterval, config.HealthCheckTimeout),
		metrics:         NewLoadBalancerMetrics(),
		config:          config,
		logger:          logger,
	}

	// 注册默认策略
	ilb.registerDefaultStrategies()

	return ilb
}

// RegisterAgent 注册Agent节点
func (ilb *IntelligentLoadBalancer) RegisterAgent(agent *AgentNode) error {
	ilb.mutex.Lock()
	defer ilb.mutex.Unlock()

	agent.CreatedAt = time.Now()
	agent.LastSeen = time.Now()
	agent.Status = NodeStatusHealthy

	// 初始化健康状态
	if agent.Health == nil {
		agent.Health = &HealthStatus{
			IsHealthy:   true,
			LastCheck:   time.Now(),
			HealthScore: 100.0,
		}
	}

	// 初始化负载指标
	if agent.Load == nil {
		agent.Load = &LoadMetrics{
			LoadScore: 0.0,
		}
	}

	ilb.agents[agent.ID] = agent
	ilb.logger.WithFields(logrus.Fields{
		"agent_id":   agent.ID,
		"agent_name": agent.Name,
		"endpoint":   agent.Endpoint,
	}).Info("Agent registered")

	return nil
}

// UnregisterAgent 注销Agent节点
func (ilb *IntelligentLoadBalancer) UnregisterAgent(agentID string) error {
	ilb.mutex.Lock()
	defer ilb.mutex.Unlock()

	if _, exists := ilb.agents[agentID]; !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}

	delete(ilb.agents, agentID)
	ilb.logger.WithField("agent_id", agentID).Info("Agent unregistered")

	return nil
}

// SelectAgent 选择Agent
func (ilb *IntelligentLoadBalancer) SelectAgent(ctx context.Context, request *LoadBalanceRequest) (*AgentNode, error) {
	start := time.Now()
	defer func() {
		ilb.updateMetrics(time.Since(start))
	}()

	// 获取健康的Agent列表
	healthyAgents := ilb.getHealthyAgents(request.RequiredCapabilities)
	if len(healthyAgents) == 0 {
		ilb.metrics.FailedRequests++
		return nil, fmt.Errorf("no healthy agents available")
	}

	// 获取当前策略
	strategy := ilb.getStrategy(ilb.currentStrategy)
	if strategy == nil {
		ilb.metrics.FailedRequests++
		return nil, fmt.Errorf("load balance strategy not found: %s", ilb.currentStrategy)
	}

	// 选择Agent
	selectedAgent, err := strategy.SelectAgent(ctx, healthyAgents, request)
	if err != nil {
		ilb.metrics.FailedRequests++
		return nil, fmt.Errorf("failed to select agent: %w", err)
	}

	// 更新指标
	ilb.metrics.SuccessfulRequests++
	ilb.metrics.TotalRequests++
	if ilb.metrics.AgentDistribution == nil {
		ilb.metrics.AgentDistribution = make(map[string]int64)
	}
	ilb.metrics.AgentDistribution[selectedAgent.ID]++

	if ilb.metrics.StrategyUsage == nil {
		ilb.metrics.StrategyUsage = make(map[string]int64)
	}
	ilb.metrics.StrategyUsage[strategy.GetName()]++

	ilb.logger.WithFields(logrus.Fields{
		"request_id":  request.RequestID,
		"agent_id":    selectedAgent.ID,
		"agent_name":  selectedAgent.Name,
		"strategy":    strategy.GetName(),
		"duration_ms": time.Since(start).Milliseconds(),
	}).Debug("Agent selected")

	return selectedAgent, nil
}

// UpdateAgentHealth 更新Agent健康状态
func (ilb *IntelligentLoadBalancer) UpdateAgentHealth(agentID string, health *HealthStatus) error {
	ilb.mutex.Lock()
	defer ilb.mutex.Unlock()

	agent, exists := ilb.agents[agentID]
	if !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}

	agent.Health = health
	agent.LastSeen = time.Now()

	// 根据健康状态更新节点状态
	if health.IsHealthy {
		if agent.Status == NodeStatusUnhealthy {
			agent.Status = NodeStatusHealthy
			ilb.logger.WithField("agent_id", agentID).Info("Agent recovered")
		}
	} else {
		if agent.Status == NodeStatusHealthy {
			agent.Status = NodeStatusUnhealthy
			ilb.logger.WithField("agent_id", agentID).Warn("Agent became unhealthy")
		}
	}

	return nil
}

// UpdateAgentLoad 更新Agent负载
func (ilb *IntelligentLoadBalancer) UpdateAgentLoad(agentID string, load *LoadMetrics) error {
	ilb.mutex.Lock()
	defer ilb.mutex.Unlock()

	agent, exists := ilb.agents[agentID]
	if !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}

	agent.Load = load
	agent.LastSeen = time.Now()

	// 检查是否需要自动扩缩容
	if ilb.config.EnableAutoScaling {
		ilb.checkAutoScaling(agent)
	}

	return nil
}

// SetStrategy 设置负载均衡策略
func (ilb *IntelligentLoadBalancer) SetStrategy(strategyName string) error {
	ilb.mutex.Lock()
	defer ilb.mutex.Unlock()

	if _, exists := ilb.strategies[strategyName]; !exists {
		return fmt.Errorf("strategy not found: %s", strategyName)
	}

	ilb.currentStrategy = strategyName
	ilb.logger.WithField("strategy", strategyName).Info("Load balance strategy changed")

	return nil
}

// GetMetrics 获取负载均衡器指标
func (ilb *IntelligentLoadBalancer) GetMetrics() *LoadBalancerMetrics {
	ilb.mutex.RLock()
	defer ilb.mutex.RUnlock()

	// 更新健康Agent统计
	healthyCount := 0
	unhealthyCount := 0
	for _, agent := range ilb.agents {
		if agent.Status == NodeStatusHealthy {
			healthyCount++
		} else {
			unhealthyCount++
		}
	}

	ilb.metrics.HealthyAgents = healthyCount
	ilb.metrics.UnhealthyAgents = unhealthyCount
	ilb.metrics.LastUpdate = time.Now()

	// 返回指标副本
	metrics := *ilb.metrics
	return &metrics
}

// GetAgents 获取所有Agent节点
func (ilb *IntelligentLoadBalancer) GetAgents() map[string]*AgentNode {
	ilb.mutex.RLock()
	defer ilb.mutex.RUnlock()

	agents := make(map[string]*AgentNode)
	for id, agent := range ilb.agents {
		agentCopy := *agent
		agents[id] = &agentCopy
	}

	return agents
}

// 私有方法

func (ilb *IntelligentLoadBalancer) getHealthyAgents(requiredCapabilities []string) []*AgentNode {
	ilb.mutex.RLock()
	defer ilb.mutex.RUnlock()

	var healthyAgents []*AgentNode
	for _, agent := range ilb.agents {
		if agent.Status != NodeStatusHealthy {
			continue
		}

		// 检查是否具备所需能力
		if len(requiredCapabilities) > 0 && !ilb.hasRequiredCapabilities(agent, requiredCapabilities) {
			continue
		}

		healthyAgents = append(healthyAgents, agent)
	}

	return healthyAgents
}

func (ilb *IntelligentLoadBalancer) hasRequiredCapabilities(agent *AgentNode, required []string) bool {
	agentCaps := make(map[string]bool)
	for _, cap := range agent.Capabilities {
		agentCaps[cap] = true
	}

	for _, req := range required {
		if !agentCaps[req] {
			return false
		}
	}

	return true
}

func (ilb *IntelligentLoadBalancer) getStrategy(name string) LoadBalanceStrategy {
	ilb.mutex.RLock()
	defer ilb.mutex.RUnlock()

	return ilb.strategies[name]
}

func (ilb *IntelligentLoadBalancer) updateMetrics(duration time.Duration) {
	ilb.mutex.Lock()
	defer ilb.mutex.Unlock()

	// 更新平均响应时间
	if ilb.metrics.TotalRequests > 0 {
		ilb.metrics.AvgResponseTime = (ilb.metrics.AvgResponseTime*float64(ilb.metrics.TotalRequests-1) + float64(duration.Milliseconds())) / float64(ilb.metrics.TotalRequests)
	} else {
		ilb.metrics.AvgResponseTime = float64(duration.Milliseconds())
	}
}

func (ilb *IntelligentLoadBalancer) checkAutoScaling(agent *AgentNode) {
	if agent.Load.LoadScore > ilb.config.LoadThreshold {
		ilb.logger.WithFields(logrus.Fields{
			"agent_id":    agent.ID,
			"load_score":  agent.Load.LoadScore,
			"threshold":   ilb.config.LoadThreshold,
		}).Info("Agent load threshold exceeded, consider scaling")
	}
}

func (ilb *IntelligentLoadBalancer) registerDefaultStrategies() {
	// 注册加权轮询策略
	ilb.strategies["weighted_round_robin"] = &WeightedRoundRobinStrategy{}

	// 注册最少连接策略
	ilb.strategies["least_connections"] = &LeastConnectionsStrategy{}

	// 注册智能策略
	ilb.strategies["intelligent"] = &IntelligentStrategy{}

	// 注册负载感知策略
	ilb.strategies["load_aware"] = &LoadAwareStrategy{}
}

// 负载均衡策略实现

// WeightedRoundRobinStrategy 加权轮询策略
type WeightedRoundRobinStrategy struct {
	currentIndex int
	mutex        sync.Mutex
}

func (wrrs *WeightedRoundRobinStrategy) SelectAgent(ctx context.Context, agents []*AgentNode, request *LoadBalanceRequest) (*AgentNode, error) {
	wrrs.mutex.Lock()
	defer wrrs.mutex.Unlock()

	if len(agents) == 0 {
		return nil, fmt.Errorf("no agents available")
	}

	// 简化的加权轮询实现
	wrrs.currentIndex = (wrrs.currentIndex + 1) % len(agents)
	return agents[wrrs.currentIndex], nil
}

func (wrrs *WeightedRoundRobinStrategy) GetName() string {
	return "weighted_round_robin"
}

func (wrrs *WeightedRoundRobinStrategy) GetDescription() string {
	return "Weighted round robin load balancing strategy"
}

// LeastConnectionsStrategy 最少连接策略
type LeastConnectionsStrategy struct{}

func (lcs *LeastConnectionsStrategy) SelectAgent(ctx context.Context, agents []*AgentNode, request *LoadBalanceRequest) (*AgentNode, error) {
	if len(agents) == 0 {
		return nil, fmt.Errorf("no agents available")
	}

	// 选择活跃连接数最少的Agent
	var selectedAgent *AgentNode
	minConnections := math.MaxInt32

	for _, agent := range agents {
		if agent.Load.ActiveRequests < minConnections {
			minConnections = agent.Load.ActiveRequests
			selectedAgent = agent
		}
	}

	return selectedAgent, nil
}

func (lcs *LeastConnectionsStrategy) GetName() string {
	return "least_connections"
}

func (lcs *LeastConnectionsStrategy) GetDescription() string {
	return "Least connections load balancing strategy"
}

// IntelligentStrategy 智能策略
type IntelligentStrategy struct{}

func (is *IntelligentStrategy) SelectAgent(ctx context.Context, agents []*AgentNode, request *LoadBalanceRequest) (*AgentNode, error) {
	if len(agents) == 0 {
		return nil, fmt.Errorf("no agents available")
	}

	// 计算每个Agent的综合得分
	type agentScore struct {
		agent *AgentNode
		score float64
	}

	var scores []agentScore
	for _, agent := range agents {
		score := is.calculateAgentScore(agent, request)
		scores = append(scores, agentScore{agent: agent, score: score})
	}

	// 按得分排序，选择得分最高的
	sort.Slice(scores, func(i, j int) bool {
		return scores[i].score > scores[j].score
	})

	return scores[0].agent, nil
}

func (is *IntelligentStrategy) calculateAgentScore(agent *AgentNode, request *LoadBalanceRequest) float64 {
	score := 100.0

	// 健康得分权重 40%
	score = score * 0.4 * (agent.Health.HealthScore / 100.0)

	// 负载得分权重 30% (负载越低得分越高)
	loadScore := math.Max(0, 100.0-agent.Load.LoadScore)
	score += score * 0.3 * (loadScore / 100.0)

	// 权重得分权重 20%
	score += score * 0.2 * (float64(agent.Weight) / 100.0)

	// 优先级得分权重 10%
	score += score * 0.1 * (float64(agent.Priority) / 10.0)

	return score
}

func (is *IntelligentStrategy) GetName() string {
	return "intelligent"
}

func (is *IntelligentStrategy) GetDescription() string {
	return "Intelligent load balancing strategy based on multiple factors"
}

// LoadAwareStrategy 负载感知策略
type LoadAwareStrategy struct{}

func (las *LoadAwareStrategy) SelectAgent(ctx context.Context, agents []*AgentNode, request *LoadBalanceRequest) (*AgentNode, error) {
	if len(agents) == 0 {
		return nil, fmt.Errorf("no agents available")
	}

	// 选择负载得分最低的Agent
	var selectedAgent *AgentNode
	minLoadScore := math.MaxFloat64

	for _, agent := range agents {
		if agent.Load.LoadScore < minLoadScore {
			minLoadScore = agent.Load.LoadScore
			selectedAgent = agent
		}
	}

	return selectedAgent, nil
}

func (las *LoadAwareStrategy) GetName() string {
	return "load_aware"
}

func (las *LoadAwareStrategy) GetDescription() string {
	return "Load aware load balancing strategy"
}

// 辅助函数

func NewLoadBalancerMetrics() *LoadBalancerMetrics {
	return &LoadBalancerMetrics{
		AgentDistribution: make(map[string]int64),
		StrategyUsage:     make(map[string]int64),
		LastUpdate:        time.Now(),
	}
}

// HealthChecker 健康检查器
type HealthChecker struct {
	interval time.Duration
	timeout  time.Duration
}

func NewHealthChecker(interval, timeout time.Duration) *HealthChecker {
	return &HealthChecker{
		interval: interval,
		timeout:  timeout,
	}
}
