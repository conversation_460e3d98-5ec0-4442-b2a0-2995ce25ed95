package agent

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
)

// TestScenario 测试场景
type TestScenario struct {
	Name             string                 `json:"name"`
	Description      string                 `json:"description"`
	UserMessage      string                 `json:"user_message"`
	ExpectedAgents   []string               `json:"expected_agents"`
	ExpectedStrategy ExecutionStrategy      `json:"expected_strategy"`
	Context          map[string]interface{} `json:"context"`
}

// TestResult 测试结果
type TestResult struct {
	Scenario       TestScenario      `json:"scenario"`
	Success        bool              `json:"success"`
	ActualAgents   []string          `json:"actual_agents"`
	ActualStrategy ExecutionStrategy `json:"actual_strategy"`
	Confidence     float64           `json:"confidence"`
	ProcessingTime time.Duration     `json:"processing_time"`
	Error          string            `json:"error,omitempty"`
	Response       string            `json:"response"`
}

// AgentPlatformTester Agent平台测试器
type AgentPlatformTester struct {
	platform *AgentPlatform
	logger   *logrus.Logger
}

// NewAgentPlatformTester 创建测试器
func NewAgentPlatformTester(platform *AgentPlatform, logger *logrus.Logger) *AgentPlatformTester {
	return &AgentPlatformTester{
		platform: platform,
		logger:   logger,
	}
}

// GetTestScenarios 获取测试场景
func (apt *AgentPlatformTester) GetTestScenarios() []TestScenario {
	return []TestScenario{
		{
			Name:             "系统状态检查",
			Description:      "测试系统监控Agent的状态检查功能",
			UserMessage:      "检查**************的系统状态",
			ExpectedAgents:   []string{"system_monitoring_agent"},
			ExpectedStrategy: StrategySequential,
			Context: map[string]interface{}{
				"host_id": int64(1),
			},
		},
		{
			Name:             "主机添加",
			Description:      "测试主机管理Agent的添加主机功能",
			UserMessage:      "添加主机************* root password123",
			ExpectedAgents:   []string{"host_management_agent"},
			ExpectedStrategy: StrategySequential,
			Context:          map[string]interface{}{},
		},
		{
			Name:             "命令执行",
			Description:      "测试命令执行Agent的基本功能",
			UserMessage:      "在服务器上执行ls -la命令",
			ExpectedAgents:   []string{"command_execution_agent"},
			ExpectedStrategy: StrategySequential,
			Context: map[string]interface{}{
				"host_id": int64(1),
			},
		},
		{
			Name:             "网络连通性测试",
			Description:      "测试网络诊断Agent的ping功能",
			UserMessage:      "ping测试*******的连通性",
			ExpectedAgents:   []string{"network_diagnosis_agent"},
			ExpectedStrategy: StrategySequential,
			Context: map[string]interface{}{
				"host_id": int64(1),
			},
		},
		{
			Name:             "日志搜索",
			Description:      "测试日志分析Agent的搜索功能",
			UserMessage:      "搜索/var/log/syslog中的错误信息",
			ExpectedAgents:   []string{"log_analysis_agent"},
			ExpectedStrategy: StrategySequential,
			Context: map[string]interface{}{
				"host_id": int64(1),
			},
		},
		{
			Name:             "安全审计",
			Description:      "测试安全检查Agent的权限审计功能",
			UserMessage:      "执行系统安全审计",
			ExpectedAgents:   []string{"security_check_agent"},
			ExpectedStrategy: StrategySequential,
			Context: map[string]interface{}{
				"host_id": int64(1),
			},
		},
		{
			Name:             "数据库备份",
			Description:      "测试备份恢复Agent的数据库备份功能",
			UserMessage:      "备份MySQL数据库",
			ExpectedAgents:   []string{"backup_restore_agent"},
			ExpectedStrategy: StrategySequential,
			Context: map[string]interface{}{
				"host_id": int64(1),
				"db_type": "mysql",
				"db_name": "test_db",
			},
		},
		{
			Name:             "CPU监控",
			Description:      "测试系统监控Agent的CPU监控功能",
			UserMessage:      "监控服务器CPU使用率",
			ExpectedAgents:   []string{"system_monitoring_agent"},
			ExpectedStrategy: StrategySequential,
			Context: map[string]interface{}{
				"host_id": int64(1),
			},
		},
		{
			Name:             "端口扫描",
			Description:      "测试网络诊断Agent的端口扫描功能",
			UserMessage:      "扫描*************的开放端口",
			ExpectedAgents:   []string{"network_diagnosis_agent"},
			ExpectedStrategy: StrategySequential,
			Context: map[string]interface{}{
				"host_id": int64(1),
			},
		},
		{
			Name:             "批量命令执行",
			Description:      "测试命令执行Agent的批量执行功能",
			UserMessage:      "批量执行系统检查命令",
			ExpectedAgents:   []string{"command_execution_agent"},
			ExpectedStrategy: StrategySequential,
			Context: map[string]interface{}{
				"host_id": int64(1),
			},
		},
		{
			Name:             "日志错误分析",
			Description:      "测试日志分析Agent的错误分析功能",
			UserMessage:      "分析系统日志中的错误",
			ExpectedAgents:   []string{"log_analysis_agent"},
			ExpectedStrategy: StrategySequential,
			Context: map[string]interface{}{
				"host_id": int64(1),
			},
		},
		{
			Name:             "漏洞扫描",
			Description:      "测试安全检查Agent的漏洞扫描功能",
			UserMessage:      "执行系统漏洞扫描",
			ExpectedAgents:   []string{"security_check_agent"},
			ExpectedStrategy: StrategySequential,
			Context: map[string]interface{}{
				"host_id": int64(1),
			},
		},
		{
			Name:             "文件系统备份",
			Description:      "测试备份恢复Agent的文件系统备份功能",
			UserMessage:      "备份/etc目录",
			ExpectedAgents:   []string{"backup_restore_agent"},
			ExpectedStrategy: StrategySequential,
			Context: map[string]interface{}{
				"host_id": int64(1),
			},
		},
		{
			Name:             "多Agent协作场景",
			Description:      "测试多Agent协作的复杂场景",
			UserMessage:      "备份数据库并检查网络连通性",
			ExpectedAgents:   []string{"backup_restore_agent", "network_diagnosis_agent"},
			ExpectedStrategy: StrategySequential,
			Context: map[string]interface{}{
				"host_id":     int64(1),
				"database":    "production_db",
				"backup_path": "/backup/",
			},
		},
		{
			Name:             "安全检查和日志分析协作",
			Description:      "测试安全检查和日志分析的协作场景",
			UserMessage:      "执行安全审计并分析相关日志",
			ExpectedAgents:   []string{"security_check_agent", "log_analysis_agent"},
			ExpectedStrategy: StrategySequential,
			Context: map[string]interface{}{
				"host_id": int64(1),
			},
		},
		{
			Name:             "全面系统检查",
			Description:      "测试多Agent的全面系统检查场景",
			UserMessage:      "执行全面的系统健康检查",
			ExpectedAgents:   []string{"system_monitoring_agent", "security_check_agent", "network_diagnosis_agent"},
			ExpectedStrategy: StrategyParallel,
			Context: map[string]interface{}{
				"host_id": int64(1),
			},
		},
	}
}

// RunTestScenario 运行单个测试场景
func (apt *AgentPlatformTester) RunTestScenario(ctx context.Context, scenario TestScenario) *TestResult {
	start := time.Now()

	apt.logger.WithFields(logrus.Fields{
		"scenario": scenario.Name,
		"message":  scenario.UserMessage,
	}).Info("Running test scenario")

	// 创建平台请求
	req := &PlatformRequest{
		UserMessage: scenario.UserMessage,
		SessionID:   fmt.Sprintf("test_%s_%d", scenario.Name, time.Now().Unix()),
		UserID:      0,
		TraceID:     fmt.Sprintf("trace_test_%d", time.Now().UnixNano()),
		Context:     scenario.Context,
		Options:     map[string]interface{}{"test_mode": true},
	}

	// 执行请求
	response, err := apt.platform.ProcessRequest(ctx, req)
	processingTime := time.Since(start)

	result := &TestResult{
		Scenario:       scenario,
		ProcessingTime: processingTime,
	}

	if err != nil {
		result.Success = false
		result.Error = err.Error()
		apt.logger.WithError(err).WithField("scenario", scenario.Name).Error("Test scenario failed")
		return result
	}

	// 提取实际结果
	if response.DecisionResult != nil {
		result.Confidence = response.DecisionResult.Confidence
		result.ActualAgents = make([]string, len(response.DecisionResult.Agents))
		for i, agent := range response.DecisionResult.Agents {
			result.ActualAgents[i] = agent.AgentID
		}

		if response.DecisionResult.ExecutionPlan != nil {
			result.ActualStrategy = response.DecisionResult.ExecutionPlan.Strategy
		}
	}

	result.Response = response.Message

	// 验证结果
	result.Success = apt.validateResult(scenario, result)

	if result.Success {
		apt.logger.WithFields(logrus.Fields{
			"scenario":        scenario.Name,
			"confidence":      result.Confidence,
			"processing_time": processingTime,
		}).Info("Test scenario passed")
	} else {
		apt.logger.WithFields(logrus.Fields{
			"scenario":          scenario.Name,
			"expected_agents":   scenario.ExpectedAgents,
			"actual_agents":     result.ActualAgents,
			"expected_strategy": scenario.ExpectedStrategy,
			"actual_strategy":   result.ActualStrategy,
		}).Warn("Test scenario failed validation")
	}

	return result
}

// RunAllTestScenarios 运行所有测试场景
func (apt *AgentPlatformTester) RunAllTestScenarios(ctx context.Context) []*TestResult {
	scenarios := apt.GetTestScenarios()
	results := make([]*TestResult, len(scenarios))

	apt.logger.WithField("total_scenarios", len(scenarios)).Info("Starting all test scenarios")

	for i, scenario := range scenarios {
		results[i] = apt.RunTestScenario(ctx, scenario)

		// 测试间隔，避免过快执行
		if i < len(scenarios)-1 {
			time.Sleep(1 * time.Second)
		}
	}

	// 生成测试报告
	apt.generateTestReport(results)

	return results
}

// validateResult 验证测试结果
func (apt *AgentPlatformTester) validateResult(scenario TestScenario, result *TestResult) bool {
	// 检查Agent选择是否正确
	if len(scenario.ExpectedAgents) > 0 {
		if len(result.ActualAgents) != len(scenario.ExpectedAgents) {
			return false
		}

		// 检查Agent是否匹配（顺序可能不同）
		expectedMap := make(map[string]bool)
		for _, agent := range scenario.ExpectedAgents {
			expectedMap[agent] = true
		}

		for _, agent := range result.ActualAgents {
			if !expectedMap[agent] {
				return false
			}
		}
	}

	// 检查执行策略
	if scenario.ExpectedStrategy != "" && result.ActualStrategy != scenario.ExpectedStrategy {
		return false
	}

	// 检查置信度
	if result.Confidence < 0.6 {
		return false
	}

	return true
}

// generateTestReport 生成测试报告
func (apt *AgentPlatformTester) generateTestReport(results []*TestResult) {
	totalTests := len(results)
	passedTests := 0
	totalProcessingTime := time.Duration(0)

	for _, result := range results {
		if result.Success {
			passedTests++
		}
		totalProcessingTime += result.ProcessingTime
	}

	successRate := float64(passedTests) / float64(totalTests) * 100
	avgProcessingTime := totalProcessingTime / time.Duration(totalTests)

	apt.logger.WithFields(logrus.Fields{
		"total_tests":           totalTests,
		"passed_tests":          passedTests,
		"failed_tests":          totalTests - passedTests,
		"success_rate":          fmt.Sprintf("%.1f%%", successRate),
		"avg_processing_time":   avgProcessingTime,
		"total_processing_time": totalProcessingTime,
	}).Info("Test report generated")

	// 详细失败信息
	for _, result := range results {
		if !result.Success {
			apt.logger.WithFields(logrus.Fields{
				"scenario": result.Scenario.Name,
				"error":    result.Error,
				"expected": result.Scenario.ExpectedAgents,
				"actual":   result.ActualAgents,
			}).Warn("Failed test details")
		}
	}
}

// GetTestReport 获取测试报告
func (apt *AgentPlatformTester) GetTestReport(results []*TestResult) map[string]interface{} {
	totalTests := len(results)
	passedTests := 0
	failedTests := make([]map[string]interface{}, 0)
	totalProcessingTime := time.Duration(0)

	for _, result := range results {
		if result.Success {
			passedTests++
		} else {
			failedTests = append(failedTests, map[string]interface{}{
				"scenario":        result.Scenario.Name,
				"error":           result.Error,
				"expected_agents": result.Scenario.ExpectedAgents,
				"actual_agents":   result.ActualAgents,
			})
		}
		totalProcessingTime += result.ProcessingTime
	}

	successRate := float64(passedTests) / float64(totalTests) * 100
	avgProcessingTime := totalProcessingTime / time.Duration(totalTests)

	return map[string]interface{}{
		"total_tests":           totalTests,
		"passed_tests":          passedTests,
		"failed_tests_count":    totalTests - passedTests,
		"success_rate":          successRate,
		"avg_processing_time":   avgProcessingTime.String(),
		"total_processing_time": totalProcessingTime.String(),
		"failed_tests":          failedTests,
		"test_results":          results,
	}
}

// QuickTest 快速测试平台基本功能
func (apt *AgentPlatformTester) QuickTest(ctx context.Context) bool {
	apt.logger.Info("Running quick test")

	// 测试平台状态
	if apt.platform.GetStatus() != PlatformStatusRunning {
		apt.logger.Error("Platform is not running")
		return false
	}

	// 测试Agent注册
	agents := apt.platform.registry.ListAgents()
	if len(agents) == 0 {
		apt.logger.Error("No agents registered")
		return false
	}

	apt.logger.WithField("agent_count", len(agents)).Info("Agents registered")

	// 测试简单场景
	scenario := TestScenario{
		Name:           "quick_test",
		UserMessage:    "显示所有主机列表",
		ExpectedAgents: []string{"host_management_agent"},
	}

	result := apt.RunTestScenario(ctx, scenario)

	apt.logger.WithFields(logrus.Fields{
		"success":         result.Success,
		"processing_time": result.ProcessingTime,
	}).Info("Quick test completed")

	return result.Success
}
