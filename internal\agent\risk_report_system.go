package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// RiskReportSystem 风险报告系统
type RiskReportSystem struct {
	config          *RiskReportConfig
	logger          *logrus.Logger
	riskEngine      *RiskAssessmentEngine
	riskController  *DynamicRiskController
	reportGenerator *ReportGenerator
	reportScheduler *ReportScheduler
	reportStorage   *ReportStorage
	notificationMgr *ReportNotificationManager
	templateManager *ReportTemplateManager
	exportManager   *ReportExportManager
	mutex           sync.RWMutex
	isRunning       bool
	stopChan        chan struct{}
}

// RiskReportConfig 风险报告配置
type RiskReportConfig struct {
	EnableAutoGeneration bool          `json:"enable_auto_generation"`
	EnableNotifications  bool          `json:"enable_notifications"`
	EnableExport         bool          `json:"enable_export"`
	ReportRetentionDays  int           `json:"report_retention_days"`
	GenerationInterval   time.Duration `json:"generation_interval"`
	NotificationChannels []string      `json:"notification_channels"`
	ExportFormats        []string      `json:"export_formats"`
	DefaultTemplate      string        `json:"default_template"`
	MaxReportsPerUser    int           `json:"max_reports_per_user"`
	CompressionEnabled   bool          `json:"compression_enabled"`
}

// ReportGenerator 报告生成器
type ReportGenerator struct {
	logger          *logrus.Logger
	riskEngine      *RiskAssessmentEngine
	riskController  *DynamicRiskController
	templateManager *ReportTemplateManager
}

// ReportScheduler 报告调度器
type ReportScheduler struct {
	logger           *logrus.Logger
	scheduledReports map[string]*ScheduledReport
	cronJobs         map[string]*ReportCronJob
	mutex            sync.RWMutex
}

// ReportStorage 报告存储
type ReportStorage struct {
	logger  *logrus.Logger
	reports map[string]*RiskReport
	mutex   sync.RWMutex
}

// ReportNotificationManager 报告通知管理器
type ReportNotificationManager struct {
	logger            *logrus.Logger
	notificationRules []*NotificationRule
	channels          map[string]NotificationChannel
	mutex             sync.RWMutex
}

// ReportTemplateManager 报告模板管理器
type ReportTemplateManager struct {
	logger    *logrus.Logger
	templates map[string]*ReportTemplate
	mutex     sync.RWMutex
}

// ReportExportManager 报告导出管理器
type ReportExportManager struct {
	logger    *logrus.Logger
	exporters map[string]ReportExporter
	mutex     sync.RWMutex
}

// RiskReport 风险报告
type RiskReport struct {
	ID               string                   `json:"id"`
	Title            string                   `json:"title"`
	Description      string                   `json:"description"`
	Type             string                   `json:"type"`   // daily, weekly, monthly, custom, incident
	Status           string                   `json:"status"` // generating, completed, failed
	GeneratedAt      time.Time                `json:"generated_at"`
	GeneratedBy      string                   `json:"generated_by"`
	TimeRange        *TimeRange               `json:"time_range"`
	Summary          *ReportSummary           `json:"summary"`
	RiskAnalysis     *RiskAnalysisSection     `json:"risk_analysis"`
	TrendAnalysis    *TrendAnalysisSection    `json:"trend_analysis"`
	IncidentAnalysis *IncidentAnalysisSection `json:"incident_analysis"`
	Recommendations  *RecommendationsSection  `json:"recommendations"`
	Appendices       *AppendicesSection       `json:"appendices"`
	Metadata         map[string]interface{}   `json:"metadata"`
	Tags             []string                 `json:"tags"`
	Recipients       []string                 `json:"recipients"`
	ExportFormats    []string                 `json:"export_formats"`
	FilePaths        map[string]string        `json:"file_paths"`
}

// TimeRange 时间范围
type TimeRange struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Duration  string    `json:"duration"`
}

// ReportSummary 报告摘要
type ReportSummary struct {
	TotalCommands        int64    `json:"total_commands"`
	HighRiskCommands     int64    `json:"high_risk_commands"`
	BlockedCommands      int64    `json:"blocked_commands"`
	SuccessfulCommands   int64    `json:"successful_commands"`
	FailedCommands       int64    `json:"failed_commands"`
	AverageRiskScore     float64  `json:"average_risk_score"`
	MaxRiskScore         float64  `json:"max_risk_score"`
	RiskTrendDirection   string   `json:"risk_trend_direction"`
	EmergencyActivations int      `json:"emergency_activations"`
	PolicyViolations     int64    `json:"policy_violations"`
	TopRiskCommands      []string `json:"top_risk_commands"`
	TopUsers             []string `json:"top_users"`
}

// RiskAnalysisSection 风险分析部分
type RiskAnalysisSection struct {
	RiskDistribution    *RiskDistribution    `json:"risk_distribution"`
	CommandAnalysis     *CommandAnalysis     `json:"command_analysis"`
	UserAnalysis        *UserAnalysis        `json:"user_analysis"`
	EnvironmentAnalysis *EnvironmentAnalysis `json:"environment_analysis"`
	TimeAnalysis        *TimeAnalysis        `json:"time_analysis"`
}

// RiskDistribution 风险分布
type RiskDistribution struct {
	LowRisk      int64              `json:"low_risk"`
	MediumRisk   int64              `json:"medium_risk"`
	HighRisk     int64              `json:"high_risk"`
	CriticalRisk int64              `json:"critical_risk"`
	Percentages  map[string]float64 `json:"percentages"`
}

// CommandAnalysis 命令分析
type CommandAnalysis struct {
	MostFrequentCommands []CommandFrequency `json:"most_frequent_commands"`
	HighestRiskCommands  []CommandRisk      `json:"highest_risk_commands"`
	BlockedCommands      []CommandBlock     `json:"blocked_commands"`
	FailedCommands       []CommandFailure   `json:"failed_commands"`
}

// CommandFrequency 命令频率
type CommandFrequency struct {
	Command    string  `json:"command"`
	Count      int64   `json:"count"`
	Percentage float64 `json:"percentage"`
}

// CommandRisk 命令风险
type CommandRisk struct {
	Command     string  `json:"command"`
	RiskScore   float64 `json:"risk_score"`
	RiskLevel   string  `json:"risk_level"`
	Occurrences int64   `json:"occurrences"`
}

// CommandBlock 命令阻止
type CommandBlock struct {
	Command  string `json:"command"`
	Count    int64  `json:"count"`
	Reason   string `json:"reason"`
	PolicyID string `json:"policy_id"`
}

// CommandFailure 命令失败
type CommandFailure struct {
	Command      string   `json:"command"`
	Count        int64    `json:"count"`
	FailureRate  float64  `json:"failure_rate"`
	CommonErrors []string `json:"common_errors"`
}

// UserAnalysis 用户分析
type UserAnalysis struct {
	MostActiveUsers  []UserActivity  `json:"most_active_users"`
	HighestRiskUsers []UserRisk      `json:"highest_risk_users"`
	PolicyViolators  []UserViolation `json:"policy_violators"`
	NewUsers         []UserInfo      `json:"new_users"`
}

// UserActivity 用户活动
type UserActivity struct {
	UserID       int64     `json:"user_id"`
	Username     string    `json:"username"`
	CommandCount int64     `json:"command_count"`
	LastActivity time.Time `json:"last_activity"`
}

// UserRisk 用户风险
type UserRisk struct {
	UserID           int64   `json:"user_id"`
	Username         string  `json:"username"`
	AverageRiskScore float64 `json:"average_risk_score"`
	HighRiskCommands int64   `json:"high_risk_commands"`
	BlockedCommands  int64   `json:"blocked_commands"`
}

// UserViolation 用户违规
type UserViolation struct {
	UserID         int64     `json:"user_id"`
	Username       string    `json:"username"`
	ViolationCount int64     `json:"violation_count"`
	LastViolation  time.Time `json:"last_violation"`
	ViolationType  string    `json:"violation_type"`
}

// UserInfo 用户信息
type UserInfo struct {
	UserID       int64     `json:"user_id"`
	Username     string    `json:"username"`
	CreatedAt    time.Time `json:"created_at"`
	FirstCommand time.Time `json:"first_command"`
}

// EnvironmentAnalysis 环境分析
type EnvironmentAnalysis struct {
	EnvironmentRisks []EnvironmentRisk   `json:"environment_risks"`
	SystemLoad       *SystemLoadAnalysis `json:"system_load"`
	SecurityEvents   []SecurityEvent     `json:"security_events"`
}

// EnvironmentRisk 环境风险
type EnvironmentRisk struct {
	Environment  string   `json:"environment"`
	RiskScore    float64  `json:"risk_score"`
	CommandCount int64    `json:"command_count"`
	Issues       []string `json:"issues"`
}

// SystemLoadAnalysis 系统负载分析
type SystemLoadAnalysis struct {
	AverageCPU    float64 `json:"average_cpu"`
	AverageMemory float64 `json:"average_memory"`
	PeakCPU       float64 `json:"peak_cpu"`
	PeakMemory    float64 `json:"peak_memory"`
	LoadSpikes    int     `json:"load_spikes"`
}

// SecurityEvent 安全事件
type SecurityEvent struct {
	EventID     string    `json:"event_id"`
	Type        string    `json:"type"`
	Severity    string    `json:"severity"`
	Description string    `json:"description"`
	Timestamp   time.Time `json:"timestamp"`
	UserID      int64     `json:"user_id"`
	Command     string    `json:"command"`
}

// TimeAnalysis 时间分析
type TimeAnalysis struct {
	HourlyDistribution []HourlyActivity  `json:"hourly_distribution"`
	DailyDistribution  []DailyActivity   `json:"daily_distribution"`
	PeakHours          []int             `json:"peak_hours"`
	OffHoursActivity   *OffHoursActivity `json:"off_hours_activity"`
}

// HourlyActivity 小时活动
type HourlyActivity struct {
	Hour         int     `json:"hour"`
	CommandCount int64   `json:"command_count"`
	RiskScore    float64 `json:"risk_score"`
}

// DailyActivity 日活动
type DailyActivity struct {
	Date         string  `json:"date"`
	CommandCount int64   `json:"command_count"`
	RiskScore    float64 `json:"risk_score"`
	Incidents    int     `json:"incidents"`
}

// OffHoursActivity 非工作时间活动
type OffHoursActivity struct {
	CommandCount       int64   `json:"command_count"`
	AverageRiskScore   float64 `json:"average_risk_score"`
	SuspiciousActivity int     `json:"suspicious_activity"`
}

// TrendAnalysisSection 趋势分析部分
type TrendAnalysisSection struct {
	RiskTrends     *RiskTrendAnalysis     `json:"risk_trends"`
	ActivityTrends *ActivityTrendAnalysis `json:"activity_trends"`
	SecurityTrends *SecurityTrendAnalysis `json:"security_trends"`
	Predictions    *TrendPredictions      `json:"predictions"`
}

// RiskTrendAnalysis 风险趋势分析
type RiskTrendAnalysis struct {
	Direction          string        `json:"direction"` // increasing, decreasing, stable
	ChangeRate         float64       `json:"change_rate"`
	TrendStartDate     time.Time     `json:"trend_start_date"`
	SignificantChanges []TrendChange `json:"significant_changes"`
}

// TrendChange 趋势变化
type TrendChange struct {
	Date        time.Time `json:"date"`
	Type        string    `json:"type"`
	Description string    `json:"description"`
	Impact      string    `json:"impact"`
}

// ActivityTrendAnalysis 活动趋势分析
type ActivityTrendAnalysis struct {
	CommandGrowthRate float64  `json:"command_growth_rate"`
	UserGrowthRate    float64  `json:"user_growth_rate"`
	PeakActivityTimes []string `json:"peak_activity_times"`
	SeasonalPatterns  []string `json:"seasonal_patterns"`
}

// SecurityTrendAnalysis 安全趋势分析
type SecurityTrendAnalysis struct {
	IncidentTrend       string  `json:"incident_trend"`
	ViolationTrend      string  `json:"violation_trend"`
	EmergencyTrend      string  `json:"emergency_trend"`
	SecurityScore       float64 `json:"security_score"`
	SecurityImprovement float64 `json:"security_improvement"`
}

// TrendPredictions 趋势预测
type TrendPredictions struct {
	NextWeekRisk       float64  `json:"next_week_risk"`
	NextMonthRisk      float64  `json:"next_month_risk"`
	PredictedIncidents int      `json:"predicted_incidents"`
	RecommendedActions []string `json:"recommended_actions"`
}

// IncidentAnalysisSection 事件分析部分
type IncidentAnalysisSection struct {
	TotalIncidents      int64              `json:"total_incidents"`
	IncidentsByType     map[string]int64   `json:"incidents_by_type"`
	IncidentsBySeverity map[string]int64   `json:"incidents_by_severity"`
	MajorIncidents      []IncidentSummary  `json:"major_incidents"`
	IncidentTimeline    []IncidentEvent    `json:"incident_timeline"`
	ResolutionMetrics   *ResolutionMetrics `json:"resolution_metrics"`
}

// IncidentSummary 事件摘要
type IncidentSummary struct {
	IncidentID    string         `json:"incident_id"`
	Type          string         `json:"type"`
	Severity      string         `json:"severity"`
	Description   string         `json:"description"`
	StartTime     time.Time      `json:"start_time"`
	EndTime       *time.Time     `json:"end_time,omitempty"`
	Duration      *time.Duration `json:"duration,omitempty"`
	AffectedUsers []int64        `json:"affected_users"`
	RootCause     string         `json:"root_cause"`
	Resolution    string         `json:"resolution"`
}

// IncidentEvent 事件事件
type IncidentEvent struct {
	Timestamp   time.Time `json:"timestamp"`
	Type        string    `json:"type"`
	Description string    `json:"description"`
	Severity    string    `json:"severity"`
	UserID      int64     `json:"user_id"`
	Command     string    `json:"command"`
}

// ResolutionMetrics 解决指标
type ResolutionMetrics struct {
	AverageResolutionTime time.Duration `json:"average_resolution_time"`
	MedianResolutionTime  time.Duration `json:"median_resolution_time"`
	FastestResolution     time.Duration `json:"fastest_resolution"`
	SlowestResolution     time.Duration `json:"slowest_resolution"`
	ResolutionRate        float64       `json:"resolution_rate"`
}

// RecommendationsSection 建议部分
type RecommendationsSection struct {
	SecurityRecommendations []Recommendation `json:"security_recommendations"`
	PolicyRecommendations   []Recommendation `json:"policy_recommendations"`
	TrainingRecommendations []Recommendation `json:"training_recommendations"`
	SystemRecommendations   []Recommendation `json:"system_recommendations"`
	PriorityActions         []PriorityAction `json:"priority_actions"`
}

// Recommendation 建议
type Recommendation struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`
	Priority    string    `json:"priority"` // high, medium, low
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Rationale   string    `json:"rationale"`
	Impact      string    `json:"impact"`
	Effort      string    `json:"effort"`
	Timeline    string    `json:"timeline"`
	Owner       string    `json:"owner"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
}

// PriorityAction 优先行动
type PriorityAction struct {
	Action      string    `json:"action"`
	Urgency     string    `json:"urgency"`
	Description string    `json:"description"`
	Deadline    time.Time `json:"deadline"`
	Assignee    string    `json:"assignee"`
}

// AppendicesSection 附录部分
type AppendicesSection struct {
	RawData         map[string]interface{} `json:"raw_data"`
	DetailedLogs    []string               `json:"detailed_logs"`
	ConfigSnapshots map[string]interface{} `json:"config_snapshots"`
	Glossary        map[string]string      `json:"glossary"`
	References      []string               `json:"references"`
}

// ReportRequest 报告请求
type ReportRequest struct {
	Type          string                 `json:"type"`
	Template      string                 `json:"template"`
	TimeRange     *TimeRange             `json:"time_range"`
	ExportFormats []string               `json:"export_formats"`
	Recipients    []string               `json:"recipients"`
	Title         string                 `json:"title"`
	Description   string                 `json:"description"`
	Tags          []string               `json:"tags"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// ReportFilter 报告过滤器
type ReportFilter struct {
	Type      string    `json:"type,omitempty"`
	Status    string    `json:"status,omitempty"`
	StartTime time.Time `json:"start_time,omitempty"`
	EndTime   time.Time `json:"end_time,omitempty"`
	Tags      []string  `json:"tags,omitempty"`
	Limit     int       `json:"limit,omitempty"`
}

// ReportNotification 报告通知
type ReportNotification struct {
	ReportID   string    `json:"report_id"`
	Type       string    `json:"type"`
	Title      string    `json:"title"`
	Message    string    `json:"message"`
	Recipients []string  `json:"recipients"`
	Timestamp  time.Time `json:"timestamp"`
	ReportURL  string    `json:"report_url"`
}

// ScheduledReport 计划报告
type ScheduledReport struct {
	ID         string     `json:"id"`
	Name       string     `json:"name"`
	Type       string     `json:"type"`
	Template   string     `json:"template"`
	Schedule   string     `json:"schedule"` // cron expression
	Recipients []string   `json:"recipients"`
	Enabled    bool       `json:"enabled"`
	LastRun    *time.Time `json:"last_run,omitempty"`
	NextRun    time.Time  `json:"next_run"`
	CreatedAt  time.Time  `json:"created_at"`
}

// ReportCronJob 报告定时任务
type ReportCronJob struct {
	ID         string         `json:"id"`
	Name       string         `json:"name"`
	Expression string         `json:"expression"`
	Request    *ReportRequest `json:"request"`
	NextRun    time.Time      `json:"next_run"`
	LastRun    *time.Time     `json:"last_run,omitempty"`
	RunCount   int64          `json:"run_count"`
	Enabled    bool           `json:"enabled"`
	CreatedAt  time.Time      `json:"created_at"`
}

// NotificationRule 通知规则
type NotificationRule struct {
	ID         string                   `json:"id"`
	Name       string                   `json:"name"`
	Conditions []*NotificationCondition `json:"conditions"`
	Channels   []string                 `json:"channels"`
	Recipients []string                 `json:"recipients"`
	Enabled    bool                     `json:"enabled"`
}

// NotificationCondition 通知条件
type NotificationCondition struct {
	Type     string      `json:"type"`
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
}

// NotificationChannel 通知通道接口
type NotificationChannel interface {
	SendNotification(notification *ReportNotification) error
	GetChannelType() string
	IsEnabled() bool
}

// ReportTemplate 报告模板
type ReportTemplate struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Type        string                 `json:"type"`
	Sections    []string               `json:"sections"`
	Layout      map[string]interface{} `json:"layout"`
	Styles      map[string]interface{} `json:"styles"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// ReportExporter 报告导出器接口
type ReportExporter interface {
	ExportReport(report *RiskReport) ([]byte, error)
	GetFormat() string
	GetMimeType() string
}

// NewRiskReportSystem 创建风险报告系统
func NewRiskReportSystem(config *RiskReportConfig, riskEngine *RiskAssessmentEngine, riskController *DynamicRiskController, logger *logrus.Logger) *RiskReportSystem {
	if config == nil {
		config = DefaultRiskReportConfig()
	}

	system := &RiskReportSystem{
		config:         config,
		logger:         logger,
		riskEngine:     riskEngine,
		riskController: riskController,
		reportStorage:  NewReportStorage(logger),
		stopChan:       make(chan struct{}),
		isRunning:      false,
	}

	// 初始化子组件
	system.templateManager = NewReportTemplateManager(logger)
	system.reportGenerator = NewReportGenerator(riskEngine, riskController, system.templateManager, logger)
	system.reportScheduler = NewReportScheduler(logger)
	system.exportManager = NewReportExportManager(logger)

	if config.EnableNotifications {
		system.notificationMgr = NewReportNotificationManager(logger)
	}

	return system
}

// DefaultRiskReportConfig 默认风险报告配置
func DefaultRiskReportConfig() *RiskReportConfig {
	return &RiskReportConfig{
		EnableAutoGeneration: true,
		EnableNotifications:  true,
		EnableExport:         true,
		ReportRetentionDays:  30,
		GenerationInterval:   24 * time.Hour,
		NotificationChannels: []string{"email"},
		ExportFormats:        []string{"json", "pdf", "html"},
		DefaultTemplate:      "standard",
		MaxReportsPerUser:    10,
		CompressionEnabled:   true,
	}
}

// Start 启动风险报告系统
func (rrs *RiskReportSystem) Start(ctx context.Context) error {
	rrs.mutex.Lock()
	defer rrs.mutex.Unlock()

	if rrs.isRunning {
		return fmt.Errorf("risk report system is already running")
	}

	rrs.logger.Info("Starting risk report system")

	// 启动报告调度器
	if rrs.config.EnableAutoGeneration {
		go rrs.autoGenerationRoutine(ctx)
	}

	// 启动清理任务
	go rrs.cleanupRoutine(ctx)

	rrs.isRunning = true
	rrs.logger.Info("Risk report system started successfully")

	return nil
}

// Stop 停止风险报告系统
func (rrs *RiskReportSystem) Stop(ctx context.Context) error {
	rrs.mutex.Lock()
	defer rrs.mutex.Unlock()

	if !rrs.isRunning {
		return nil
	}

	rrs.logger.Info("Stopping risk report system")

	close(rrs.stopChan)
	rrs.isRunning = false

	rrs.logger.Info("Risk report system stopped")
	return nil
}

// GenerateReport 生成风险报告
func (rrs *RiskReportSystem) GenerateReport(ctx context.Context, request *ReportRequest) (*RiskReport, error) {
	rrs.mutex.RLock()
	defer rrs.mutex.RUnlock()

	if !rrs.isRunning {
		return nil, fmt.Errorf("risk report system is not running")
	}

	rrs.logger.WithFields(logrus.Fields{
		"type":       request.Type,
		"time_range": request.TimeRange,
		"template":   request.Template,
	}).Info("Generating risk report")

	// 生成报告
	report, err := rrs.reportGenerator.GenerateReport(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("failed to generate report: %w", err)
	}

	// 存储报告
	if err := rrs.reportStorage.StoreReport(report); err != nil {
		rrs.logger.WithError(err).Error("Failed to store report")
	}

	// 导出报告
	if rrs.config.EnableExport && len(request.ExportFormats) > 0 {
		go rrs.exportReport(report, request.ExportFormats)
	}

	// 发送通知
	if rrs.config.EnableNotifications && len(request.Recipients) > 0 {
		go rrs.sendReportNotification(report, request.Recipients)
	}

	rrs.logger.WithField("report_id", report.ID).Info("Risk report generated successfully")

	return report, nil
}

// GetReport 获取报告
func (rrs *RiskReportSystem) GetReport(reportID string) (*RiskReport, error) {
	return rrs.reportStorage.GetReport(reportID)
}

// ListReports 列出报告
func (rrs *RiskReportSystem) ListReports(filter *ReportFilter) ([]*RiskReport, error) {
	return rrs.reportStorage.ListReports(filter)
}

// DeleteReport 删除报告
func (rrs *RiskReportSystem) DeleteReport(reportID string) error {
	return rrs.reportStorage.DeleteReport(reportID)
}

// autoGenerationRoutine 自动生成协程
func (rrs *RiskReportSystem) autoGenerationRoutine(ctx context.Context) {
	ticker := time.NewTicker(rrs.config.GenerationInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-rrs.stopChan:
			return
		case <-ticker.C:
			rrs.generateScheduledReports(ctx)
		}
	}
}

// cleanupRoutine 清理协程
func (rrs *RiskReportSystem) cleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(24 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-rrs.stopChan:
			return
		case <-ticker.C:
			rrs.cleanupOldReports()
		}
	}
}

// generateScheduledReports 生成计划报告
func (rrs *RiskReportSystem) generateScheduledReports(ctx context.Context) {
	// 生成日报
	dailyRequest := &ReportRequest{
		Type:     "daily",
		Template: rrs.config.DefaultTemplate,
		TimeRange: &TimeRange{
			StartTime: time.Now().AddDate(0, 0, -1),
			EndTime:   time.Now(),
			Duration:  "24h",
		},
		ExportFormats: rrs.config.ExportFormats,
		Recipients:    []string{"<EMAIL>"},
	}

	if _, err := rrs.GenerateReport(ctx, dailyRequest); err != nil {
		rrs.logger.WithError(err).Error("Failed to generate daily report")
	}

	// 每周生成周报
	if time.Now().Weekday() == time.Monday {
		weeklyRequest := &ReportRequest{
			Type:     "weekly",
			Template: rrs.config.DefaultTemplate,
			TimeRange: &TimeRange{
				StartTime: time.Now().AddDate(0, 0, -7),
				EndTime:   time.Now(),
				Duration:  "7d",
			},
			ExportFormats: rrs.config.ExportFormats,
			Recipients:    []string{"<EMAIL>", "<EMAIL>"},
		}

		if _, err := rrs.GenerateReport(ctx, weeklyRequest); err != nil {
			rrs.logger.WithError(err).Error("Failed to generate weekly report")
		}
	}

	// 每月生成月报
	if time.Now().Day() == 1 {
		monthlyRequest := &ReportRequest{
			Type:     "monthly",
			Template: rrs.config.DefaultTemplate,
			TimeRange: &TimeRange{
				StartTime: time.Now().AddDate(0, -1, 0),
				EndTime:   time.Now(),
				Duration:  "30d",
			},
			ExportFormats: rrs.config.ExportFormats,
			Recipients:    []string{"<EMAIL>", "<EMAIL>"},
		}

		if _, err := rrs.GenerateReport(ctx, monthlyRequest); err != nil {
			rrs.logger.WithError(err).Error("Failed to generate monthly report")
		}
	}
}

// cleanupOldReports 清理旧报告
func (rrs *RiskReportSystem) cleanupOldReports() {
	cutoffTime := time.Now().AddDate(0, 0, -rrs.config.ReportRetentionDays)

	filter := &ReportFilter{
		EndTime: cutoffTime,
	}

	reports, err := rrs.reportStorage.ListReports(filter)
	if err != nil {
		rrs.logger.WithError(err).Error("Failed to list reports for cleanup")
		return
	}

	for _, report := range reports {
		if err := rrs.reportStorage.DeleteReport(report.ID); err != nil {
			rrs.logger.WithError(err).WithField("report_id", report.ID).Error("Failed to delete old report")
		} else {
			rrs.logger.WithField("report_id", report.ID).Info("Old report deleted")
		}
	}
}

// exportReport 导出报告
func (rrs *RiskReportSystem) exportReport(report *RiskReport, formats []string) {
	if rrs.exportManager == nil {
		return
	}

	for _, format := range formats {
		if err := rrs.exportManager.ExportReport(report, format); err != nil {
			rrs.logger.WithError(err).WithFields(logrus.Fields{
				"report_id": report.ID,
				"format":    format,
			}).Error("Failed to export report")
		}
	}
}

// sendReportNotification 发送报告通知
func (rrs *RiskReportSystem) sendReportNotification(report *RiskReport, recipients []string) {
	if rrs.notificationMgr == nil {
		return
	}

	notification := &ReportNotification{
		ReportID:   report.ID,
		Type:       "report_generated",
		Title:      fmt.Sprintf("Risk Report Generated: %s", report.Title),
		Message:    fmt.Sprintf("A new risk report has been generated: %s", report.Description),
		Recipients: recipients,
		Timestamp:  time.Now(),
		ReportURL:  fmt.Sprintf("/reports/%s", report.ID),
	}

	if err := rrs.notificationMgr.SendNotification(notification); err != nil {
		rrs.logger.WithError(err).WithField("report_id", report.ID).Error("Failed to send report notification")
	}
}

// 子组件构造函数

// NewReportStorage 创建报告存储
func NewReportStorage(logger *logrus.Logger) *ReportStorage {
	return &ReportStorage{
		logger:  logger,
		reports: make(map[string]*RiskReport),
	}
}

// NewReportTemplateManager 创建报告模板管理器
func NewReportTemplateManager(logger *logrus.Logger) *ReportTemplateManager {
	manager := &ReportTemplateManager{
		logger:    logger,
		templates: make(map[string]*ReportTemplate),
	}

	// 加载默认模板
	manager.loadDefaultTemplates()

	return manager
}

// NewReportGenerator 创建报告生成器
func NewReportGenerator(riskEngine *RiskAssessmentEngine, riskController *DynamicRiskController, templateManager *ReportTemplateManager, logger *logrus.Logger) *ReportGenerator {
	return &ReportGenerator{
		logger:          logger,
		riskEngine:      riskEngine,
		riskController:  riskController,
		templateManager: templateManager,
	}
}

// NewReportScheduler 创建报告调度器
func NewReportScheduler(logger *logrus.Logger) *ReportScheduler {
	return &ReportScheduler{
		logger:           logger,
		scheduledReports: make(map[string]*ScheduledReport),
		cronJobs:         make(map[string]*ReportCronJob),
	}
}

// NewReportExportManager 创建报告导出管理器
func NewReportExportManager(logger *logrus.Logger) *ReportExportManager {
	manager := &ReportExportManager{
		logger:    logger,
		exporters: make(map[string]ReportExporter),
	}

	// 注册默认导出器
	manager.registerDefaultExporters()

	return manager
}

// NewReportNotificationManager 创建报告通知管理器
func NewReportNotificationManager(logger *logrus.Logger) *ReportNotificationManager {
	return &ReportNotificationManager{
		logger:            logger,
		notificationRules: make([]*NotificationRule, 0),
		channels:          make(map[string]NotificationChannel),
	}
}

// 报告存储方法

// StoreReport 存储报告
func (rs *ReportStorage) StoreReport(report *RiskReport) error {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	rs.reports[report.ID] = report

	rs.logger.WithField("report_id", report.ID).Info("Report stored")
	return nil
}

// GetReport 获取报告
func (rs *ReportStorage) GetReport(reportID string) (*RiskReport, error) {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()

	report, exists := rs.reports[reportID]
	if !exists {
		return nil, fmt.Errorf("report not found: %s", reportID)
	}

	return report, nil
}

// ListReports 列出报告
func (rs *ReportStorage) ListReports(filter *ReportFilter) ([]*RiskReport, error) {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()

	var reports []*RiskReport

	for _, report := range rs.reports {
		if rs.matchesFilter(report, filter) {
			reports = append(reports, report)
		}
	}

	// 按生成时间排序
	sort.Slice(reports, func(i, j int) bool {
		return reports[i].GeneratedAt.After(reports[j].GeneratedAt)
	})

	// 应用限制
	if filter != nil && filter.Limit > 0 && len(reports) > filter.Limit {
		reports = reports[:filter.Limit]
	}

	return reports, nil
}

// DeleteReport 删除报告
func (rs *ReportStorage) DeleteReport(reportID string) error {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	if _, exists := rs.reports[reportID]; !exists {
		return fmt.Errorf("report not found: %s", reportID)
	}

	delete(rs.reports, reportID)

	rs.logger.WithField("report_id", reportID).Info("Report deleted")
	return nil
}

// matchesFilter 检查报告是否匹配过滤器
func (rs *ReportStorage) matchesFilter(report *RiskReport, filter *ReportFilter) bool {
	if filter == nil {
		return true
	}

	if filter.Type != "" && report.Type != filter.Type {
		return false
	}

	if filter.Status != "" && report.Status != filter.Status {
		return false
	}

	if !filter.StartTime.IsZero() && report.GeneratedAt.Before(filter.StartTime) {
		return false
	}

	if !filter.EndTime.IsZero() && report.GeneratedAt.After(filter.EndTime) {
		return false
	}

	if len(filter.Tags) > 0 {
		hasTag := false
		for _, filterTag := range filter.Tags {
			for _, reportTag := range report.Tags {
				if filterTag == reportTag {
					hasTag = true
					break
				}
			}
			if hasTag {
				break
			}
		}
		if !hasTag {
			return false
		}
	}

	return true
}

// 报告生成器方法

// GenerateReport 生成报告
func (rg *ReportGenerator) GenerateReport(ctx context.Context, request *ReportRequest) (*RiskReport, error) {
	report := &RiskReport{
		ID:            fmt.Sprintf("report_%d", time.Now().UnixNano()),
		Title:         request.Title,
		Description:   request.Description,
		Type:          request.Type,
		Status:        "generating",
		GeneratedAt:   time.Now(),
		GeneratedBy:   "system",
		TimeRange:     request.TimeRange,
		Tags:          request.Tags,
		Recipients:    request.Recipients,
		ExportFormats: request.ExportFormats,
		Metadata:      request.Metadata,
		FilePaths:     make(map[string]string),
	}

	if report.Title == "" {
		report.Title = fmt.Sprintf("%s Risk Report", request.Type)
	}

	if report.Description == "" {
		report.Description = fmt.Sprintf("Automated %s risk analysis report", request.Type)
	}

	// 生成报告摘要
	summary, err := rg.generateSummary(ctx, request.TimeRange)
	if err != nil {
		report.Status = "failed"
		return report, fmt.Errorf("failed to generate summary: %w", err)
	}
	report.Summary = summary

	// 生成风险分析
	riskAnalysis, err := rg.generateRiskAnalysis(ctx, request.TimeRange)
	if err != nil {
		report.Status = "failed"
		return report, fmt.Errorf("failed to generate risk analysis: %w", err)
	}
	report.RiskAnalysis = riskAnalysis

	// 生成趋势分析
	trendAnalysis, err := rg.generateTrendAnalysis(ctx, request.TimeRange)
	if err != nil {
		report.Status = "failed"
		return report, fmt.Errorf("failed to generate trend analysis: %w", err)
	}
	report.TrendAnalysis = trendAnalysis

	// 生成事件分析
	incidentAnalysis, err := rg.generateIncidentAnalysis(ctx, request.TimeRange)
	if err != nil {
		report.Status = "failed"
		return report, fmt.Errorf("failed to generate incident analysis: %w", err)
	}
	report.IncidentAnalysis = incidentAnalysis

	// 生成建议
	recommendations, err := rg.generateRecommendations(ctx, report)
	if err != nil {
		report.Status = "failed"
		return report, fmt.Errorf("failed to generate recommendations: %w", err)
	}
	report.Recommendations = recommendations

	// 生成附录
	appendices, err := rg.generateAppendices(ctx, request.TimeRange)
	if err != nil {
		report.Status = "failed"
		return report, fmt.Errorf("failed to generate appendices: %w", err)
	}
	report.Appendices = appendices

	report.Status = "completed"

	rg.logger.WithField("report_id", report.ID).Info("Report generated successfully")

	return report, nil
}

// generateSummary 生成报告摘要
func (rg *ReportGenerator) generateSummary(ctx context.Context, timeRange *TimeRange) (*ReportSummary, error) {
	// 简化实现：生成模拟数据
	summary := &ReportSummary{
		TotalCommands:        1000,
		HighRiskCommands:     50,
		BlockedCommands:      10,
		SuccessfulCommands:   940,
		FailedCommands:       50,
		AverageRiskScore:     0.3,
		MaxRiskScore:         0.9,
		RiskTrendDirection:   "stable",
		EmergencyActivations: 2,
		PolicyViolations:     15,
		TopRiskCommands:      []string{"rm -rf", "dd of=", "chmod 777"},
		TopUsers:             []string{"admin", "operator", "developer"},
	}

	return summary, nil
}

// generateRiskAnalysis 生成风险分析
func (rg *ReportGenerator) generateRiskAnalysis(ctx context.Context, timeRange *TimeRange) (*RiskAnalysisSection, error) {
	analysis := &RiskAnalysisSection{
		RiskDistribution: &RiskDistribution{
			LowRisk:      800,
			MediumRisk:   150,
			HighRisk:     40,
			CriticalRisk: 10,
			Percentages: map[string]float64{
				"low":      80.0,
				"medium":   15.0,
				"high":     4.0,
				"critical": 1.0,
			},
		},
		CommandAnalysis: &CommandAnalysis{
			MostFrequentCommands: []CommandFrequency{
				{Command: "ls", Count: 200, Percentage: 20.0},
				{Command: "cd", Count: 150, Percentage: 15.0},
				{Command: "cat", Count: 100, Percentage: 10.0},
			},
			HighestRiskCommands: []CommandRisk{
				{Command: "rm -rf", RiskScore: 0.9, RiskLevel: "critical", Occurrences: 5},
				{Command: "dd of=", RiskScore: 0.85, RiskLevel: "high", Occurrences: 3},
				{Command: "chmod 777", RiskScore: 0.8, RiskLevel: "high", Occurrences: 8},
			},
			BlockedCommands: []CommandBlock{
				{Command: "rm -rf /", Count: 3, Reason: "极高风险", PolicyID: "critical_risk_policy"},
				{Command: "dd of=/dev/sda", Count: 2, Reason: "磁盘写入", PolicyID: "disk_protection_policy"},
			},
			FailedCommands: []CommandFailure{
				{Command: "mount", Count: 10, FailureRate: 0.2, CommonErrors: []string{"权限不足", "设备不存在"}},
				{Command: "systemctl", Count: 8, FailureRate: 0.15, CommonErrors: []string{"服务不存在", "权限不足"}},
			},
		},
		UserAnalysis: &UserAnalysis{
			MostActiveUsers: []UserActivity{
				{UserID: 1, Username: "admin", CommandCount: 300, LastActivity: time.Now()},
				{UserID: 2, Username: "operator", CommandCount: 250, LastActivity: time.Now().Add(-1 * time.Hour)},
			},
			HighestRiskUsers: []UserRisk{
				{UserID: 1, Username: "admin", AverageRiskScore: 0.6, HighRiskCommands: 20, BlockedCommands: 5},
				{UserID: 3, Username: "developer", AverageRiskScore: 0.4, HighRiskCommands: 15, BlockedCommands: 2},
			},
		},
		EnvironmentAnalysis: &EnvironmentAnalysis{
			EnvironmentRisks: []EnvironmentRisk{
				{Environment: "production", RiskScore: 0.7, CommandCount: 500, Issues: []string{"高风险命令频繁", "非工作时间活动"}},
				{Environment: "staging", RiskScore: 0.4, CommandCount: 300, Issues: []string{"测试数据泄露风险"}},
			},
			SystemLoad: &SystemLoadAnalysis{
				AverageCPU:    45.0,
				AverageMemory: 60.0,
				PeakCPU:       85.0,
				PeakMemory:    90.0,
				LoadSpikes:    5,
			},
		},
		TimeAnalysis: &TimeAnalysis{
			HourlyDistribution: []HourlyActivity{
				{Hour: 9, CommandCount: 80, RiskScore: 0.3},
				{Hour: 14, CommandCount: 120, RiskScore: 0.4},
				{Hour: 22, CommandCount: 30, RiskScore: 0.7},
			},
			PeakHours: []int{9, 14, 16},
			OffHoursActivity: &OffHoursActivity{
				CommandCount:       50,
				AverageRiskScore:   0.6,
				SuspiciousActivity: 3,
			},
		},
	}

	return analysis, nil
}

// generateTrendAnalysis 生成趋势分析
func (rg *ReportGenerator) generateTrendAnalysis(ctx context.Context, timeRange *TimeRange) (*TrendAnalysisSection, error) {
	analysis := &TrendAnalysisSection{
		RiskTrends: &RiskTrendAnalysis{
			Direction:      "stable",
			ChangeRate:     0.05,
			TrendStartDate: time.Now().AddDate(0, 0, -7),
			SignificantChanges: []TrendChange{
				{
					Date:        time.Now().AddDate(0, 0, -3),
					Type:        "risk_increase",
					Description: "高风险命令增加",
					Impact:      "medium",
				},
			},
		},
		ActivityTrends: &ActivityTrendAnalysis{
			CommandGrowthRate: 0.1,
			UserGrowthRate:    0.05,
			PeakActivityTimes: []string{"09:00-10:00", "14:00-15:00"},
			SeasonalPatterns:  []string{"工作日活跃", "周末低活跃"},
		},
		SecurityTrends: &SecurityTrendAnalysis{
			IncidentTrend:       "decreasing",
			ViolationTrend:      "stable",
			EmergencyTrend:      "decreasing",
			SecurityScore:       0.85,
			SecurityImprovement: 0.1,
		},
		Predictions: &TrendPredictions{
			NextWeekRisk:       0.35,
			NextMonthRisk:      0.4,
			PredictedIncidents: 2,
			RecommendedActions: []string{"加强用户培训", "更新安全策略", "增加监控频率"},
		},
	}

	return analysis, nil
}

// generateIncidentAnalysis 生成事件分析
func (rg *ReportGenerator) generateIncidentAnalysis(ctx context.Context, timeRange *TimeRange) (*IncidentAnalysisSection, error) {
	analysis := &IncidentAnalysisSection{
		TotalIncidents: 5,
		IncidentsByType: map[string]int64{
			"security_violation": 2,
			"policy_violation":   2,
			"system_error":       1,
		},
		IncidentsBySeverity: map[string]int64{
			"low":      2,
			"medium":   2,
			"high":     1,
			"critical": 0,
		},
		MajorIncidents: []IncidentSummary{
			{
				IncidentID:    "INC-001",
				Type:          "security_violation",
				Severity:      "high",
				Description:   "未授权的高风险命令执行",
				StartTime:     time.Now().Add(-24 * time.Hour),
				EndTime:       &[]time.Time{time.Now().Add(-23 * time.Hour)}[0],
				Duration:      &[]time.Duration{1 * time.Hour}[0],
				AffectedUsers: []int64{3},
				RootCause:     "用户权限配置错误",
				Resolution:    "更新用户权限，加强监控",
			},
		},
		IncidentTimeline: []IncidentEvent{
			{
				Timestamp:   time.Now().Add(-24 * time.Hour),
				Type:        "security_violation",
				Description: "检测到高风险命令执行",
				Severity:    "high",
				UserID:      3,
				Command:     "rm -rf /tmp/*",
			},
		},
		ResolutionMetrics: &ResolutionMetrics{
			AverageResolutionTime: 2 * time.Hour,
			MedianResolutionTime:  1 * time.Hour,
			FastestResolution:     30 * time.Minute,
			SlowestResolution:     4 * time.Hour,
			ResolutionRate:        0.95,
		},
	}

	return analysis, nil
}

// generateRecommendations 生成建议
func (rg *ReportGenerator) generateRecommendations(ctx context.Context, report *RiskReport) (*RecommendationsSection, error) {
	recommendations := &RecommendationsSection{
		SecurityRecommendations: []Recommendation{
			{
				ID:          "SEC-001",
				Type:        "security",
				Priority:    "high",
				Title:       "加强高风险命令监控",
				Description: "对rm、dd、chmod等高风险命令实施更严格的监控和控制",
				Rationale:   "报告显示高风险命令使用频率较高",
				Impact:      "显著降低安全风险",
				Effort:      "medium",
				Timeline:    "2周",
				Owner:       "安全团队",
				Status:      "pending",
				CreatedAt:   time.Now(),
			},
		},
		PolicyRecommendations: []Recommendation{
			{
				ID:          "POL-001",
				Type:        "policy",
				Priority:    "medium",
				Title:       "更新用户权限策略",
				Description: "根据用户实际需求调整权限分配",
				Rationale:   "发现部分用户权限过高",
				Impact:      "降低权限滥用风险",
				Effort:      "low",
				Timeline:    "1周",
				Owner:       "运维团队",
				Status:      "pending",
				CreatedAt:   time.Now(),
			},
		},
		TrainingRecommendations: []Recommendation{
			{
				ID:          "TRA-001",
				Type:        "training",
				Priority:    "medium",
				Title:       "安全意识培训",
				Description: "为所有用户提供安全操作培训",
				Rationale:   "用户安全意识有待提高",
				Impact:      "提升整体安全水平",
				Effort:      "medium",
				Timeline:    "1个月",
				Owner:       "培训部门",
				Status:      "pending",
				CreatedAt:   time.Now(),
			},
		},
		SystemRecommendations: []Recommendation{
			{
				ID:          "SYS-001",
				Type:        "system",
				Priority:    "low",
				Title:       "优化系统性能监控",
				Description: "增加更多系统性能指标监控",
				Rationale:   "当前监控覆盖不够全面",
				Impact:      "提升系统可观测性",
				Effort:      "high",
				Timeline:    "2个月",
				Owner:       "技术团队",
				Status:      "pending",
				CreatedAt:   time.Now(),
			},
		},
		PriorityActions: []PriorityAction{
			{
				Action:      "立即审查高风险用户权限",
				Urgency:     "high",
				Description: "对风险分数较高的用户进行权限审查",
				Deadline:    time.Now().AddDate(0, 0, 3),
				Assignee:    "安全管理员",
			},
			{
				Action:      "更新安全策略文档",
				Urgency:     "medium",
				Description: "根据最新风险评估结果更新安全策略",
				Deadline:    time.Now().AddDate(0, 0, 7),
				Assignee:    "策略制定者",
			},
		},
	}

	return recommendations, nil
}

// generateAppendices 生成附录
func (rg *ReportGenerator) generateAppendices(ctx context.Context, timeRange *TimeRange) (*AppendicesSection, error) {
	appendices := &AppendicesSection{
		RawData: map[string]interface{}{
			"total_commands":  1000,
			"analysis_period": timeRange.Duration,
			"data_sources":    []string{"audit_logs", "risk_assessments", "system_metrics"},
			"processing_time": "5.2 seconds",
		},
		DetailedLogs: []string{
			"2024-01-15 10:30:00 - High risk command detected: rm -rf",
			"2024-01-15 11:45:00 - Policy violation: unauthorized access attempt",
			"2024-01-15 14:20:00 - Emergency mode activated due to suspicious activity",
		},
		ConfigSnapshots: map[string]interface{}{
			"risk_thresholds": map[string]float64{
				"low":      0.3,
				"medium":   0.7,
				"high":     0.9,
				"critical": 1.0,
			},
			"active_policies": []string{
				"critical_risk_policy",
				"high_risk_policy",
				"production_env_policy",
			},
		},
		Glossary: map[string]string{
			"Risk Score":        "0-1之间的数值，表示命令的风险程度",
			"Policy Violation":  "违反已定义安全策略的行为",
			"Emergency Mode":    "系统检测到高风险活动时的保护模式",
			"Sandbox Execution": "在隔离环境中执行命令以降低风险",
		},
		References: []string{
			"企业安全策略文档 v2.1",
			"风险评估标准 ISO 27001",
			"系统安全最佳实践指南",
		},
	}

	return appendices, nil
}

// 模板管理器方法

// loadDefaultTemplates 加载默认模板
func (rtm *ReportTemplateManager) loadDefaultTemplates() {
	// 标准模板
	standardTemplate := &ReportTemplate{
		ID:          "standard",
		Name:        "标准风险报告模板",
		Description: "包含所有标准部分的完整风险报告模板",
		Type:        "comprehensive",
		Sections:    []string{"summary", "risk_analysis", "trend_analysis", "incident_analysis", "recommendations", "appendices"},
		Layout: map[string]interface{}{
			"page_size":   "A4",
			"orientation": "portrait",
			"margins":     "2cm",
			"font_family": "Arial",
			"font_size":   12,
		},
		Styles: map[string]interface{}{
			"header_color": "#2E86AB",
			"text_color":   "#333333",
			"accent_color": "#A23B72",
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 简化模板
	summaryTemplate := &ReportTemplate{
		ID:          "summary",
		Name:        "摘要风险报告模板",
		Description: "仅包含关键摘要信息的简化报告模板",
		Type:        "summary",
		Sections:    []string{"summary", "recommendations"},
		Layout: map[string]interface{}{
			"page_size":   "A4",
			"orientation": "portrait",
			"margins":     "1.5cm",
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	rtm.mutex.Lock()
	rtm.templates[standardTemplate.ID] = standardTemplate
	rtm.templates[summaryTemplate.ID] = summaryTemplate
	rtm.mutex.Unlock()

	rtm.logger.Info("Default report templates loaded")
}

// 导出管理器方法

// registerDefaultExporters 注册默认导出器
func (rem *ReportExportManager) registerDefaultExporters() {
	// 注册JSON导出器
	rem.exporters["json"] = &JSONExporter{}

	// 注册HTML导出器
	rem.exporters["html"] = &HTMLExporter{}

	// 注册PDF导出器（简化实现）
	rem.exporters["pdf"] = &PDFExporter{}

	rem.logger.Info("Default report exporters registered")
}

// ExportReport 导出报告
func (rem *ReportExportManager) ExportReport(report *RiskReport, format string) error {
	rem.mutex.RLock()
	exporter, exists := rem.exporters[format]
	rem.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("unsupported export format: %s", format)
	}

	data, err := exporter.ExportReport(report)
	if err != nil {
		return fmt.Errorf("failed to export report: %w", err)
	}

	// 保存文件（简化实现）
	filename := fmt.Sprintf("%s.%s", report.ID, format)
	report.FilePaths[format] = filename

	rem.logger.WithFields(logrus.Fields{
		"report_id": report.ID,
		"format":    format,
		"filename":  filename,
		"size":      len(data),
	}).Info("Report exported successfully")

	return nil
}

// 通知管理器方法

// SendNotification 发送通知
func (rnm *ReportNotificationManager) SendNotification(notification *ReportNotification) error {
	rnm.mutex.RLock()
	defer rnm.mutex.RUnlock()

	for _, channelName := range []string{"email"} { // 简化实现
		if channel, exists := rnm.channels[channelName]; exists {
			if err := channel.SendNotification(notification); err != nil {
				rnm.logger.WithError(err).WithField("channel", channelName).Error("Failed to send notification")
			}
		}
	}

	rnm.logger.WithField("notification_type", notification.Type).Info("Notification sent")
	return nil
}

// 导出器实现

// JSONExporter JSON导出器
type JSONExporter struct{}

// ExportReport 导出为JSON格式
func (je *JSONExporter) ExportReport(report *RiskReport) ([]byte, error) {
	return json.Marshal(report)
}

// GetFormat 获取格式
func (je *JSONExporter) GetFormat() string {
	return "json"
}

// GetMimeType 获取MIME类型
func (je *JSONExporter) GetMimeType() string {
	return "application/json"
}

// HTMLExporter HTML导出器
type HTMLExporter struct{}

// ExportReport 导出为HTML格式
func (he *HTMLExporter) ExportReport(report *RiskReport) ([]byte, error) {
	html := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <title>%s</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #2E86AB; color: white; padding: 20px; }
        .section { margin: 20px 0; }
        .summary { background-color: #f5f5f5; padding: 15px; border-radius: 5px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background-color: white; border-radius: 3px; }
        .risk-high { color: #d32f2f; }
        .risk-medium { color: #f57c00; }
        .risk-low { color: #388e3c; }
    </style>
</head>
<body>
    <div class="header">
        <h1>%s</h1>
        <p>%s</p>
        <p>生成时间: %s</p>
    </div>

    <div class="section">
        <h2>报告摘要</h2>
        <div class="summary">
            <div class="metric">总命令数: %d</div>
            <div class="metric">高风险命令: %d</div>
            <div class="metric">阻止命令: %d</div>
            <div class="metric">平均风险分数: %.2f</div>
        </div>
    </div>

    <div class="section">
        <h2>风险分析</h2>
        <p>详细的风险分析数据...</p>
    </div>

    <div class="section">
        <h2>建议</h2>
        <p>基于分析结果的安全建议...</p>
    </div>
</body>
</html>`,
		report.Title,
		report.Title,
		report.Description,
		report.GeneratedAt.Format("2006-01-02 15:04:05"),
		report.Summary.TotalCommands,
		report.Summary.HighRiskCommands,
		report.Summary.BlockedCommands,
		report.Summary.AverageRiskScore,
	)

	return []byte(html), nil
}

// GetFormat 获取格式
func (he *HTMLExporter) GetFormat() string {
	return "html"
}

// GetMimeType 获取MIME类型
func (he *HTMLExporter) GetMimeType() string {
	return "text/html"
}

// PDFExporter PDF导出器（简化实现）
type PDFExporter struct{}

// ExportReport 导出为PDF格式
func (pe *PDFExporter) ExportReport(report *RiskReport) ([]byte, error) {
	// 简化实现：返回PDF头部信息
	pdfContent := fmt.Sprintf("%%PDF-1.4\n1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n\nRisk Report: %s\nGenerated: %s\n",
		report.Title,
		report.GeneratedAt.Format("2006-01-02 15:04:05"),
	)

	return []byte(pdfContent), nil
}

// GetFormat 获取格式
func (pe *PDFExporter) GetFormat() string {
	return "pdf"
}

// GetMimeType 获取MIME类型
func (pe *PDFExporter) GetMimeType() string {
	return "application/pdf"
}

// GetStatus 获取风险报告系统状态
func (rrs *RiskReportSystem) GetStatus() *RiskReportSystemStatus {
	rrs.mutex.RLock()
	defer rrs.mutex.RUnlock()

	status := &RiskReportSystemStatus{
		IsRunning:      rrs.isRunning,
		TotalReports:   len(rrs.reportStorage.reports),
		Config:         rrs.config,
		LastGeneration: time.Now(), // 简化实现
	}

	// 统计报告类型
	status.ReportsByType = make(map[string]int)
	for _, report := range rrs.reportStorage.reports {
		status.ReportsByType[report.Type]++
	}

	return status
}

// RiskReportSystemStatus 风险报告系统状态
type RiskReportSystemStatus struct {
	IsRunning      bool              `json:"is_running"`
	TotalReports   int               `json:"total_reports"`
	ReportsByType  map[string]int    `json:"reports_by_type"`
	LastGeneration time.Time         `json:"last_generation"`
	Config         *RiskReportConfig `json:"config"`
}
