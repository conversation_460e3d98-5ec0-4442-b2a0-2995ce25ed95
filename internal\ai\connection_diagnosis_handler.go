package ai

import (
	"context"
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"
)

// connectionDiagnosisHandler 连接诊断场景处理器
type connectionDiagnosisHandler struct {
	deepseekService interface{}
	hostService     interface{}
	logger          *logrus.Logger
}

// NewConnectionDiagnosisHandler 创建连接诊断处理器
func NewConnectionDiagnosisHandler(
	deepseekService interface{},
	hostService interface{},
	logger *logrus.Logger,
) ScenarioHandler {
	return &connectionDiagnosisHandler{
		deepseekService: deepseekService,
		hostService:     hostService,
		logger:          logger,
	}
}

// CanHandle 检查是否能处理此场景
func (cdh *connectionDiagnosisHandler) CanHandle(intent string, entities map[string]interface{}) bool {
	if intent != IntentConnectionDiagnosis {
		return false
	}

	// 必须有IP地址或主机名
	_, hasIP := entities[EntityIPAddress]
	_, hasHostname := entities[EntityHostname]

	return hasIP || hasHostname
}

// GenerateCommands 生成命令序列
func (cdh *connectionDiagnosisHandler) GenerateCommands(ctx context.Context, classification *ClassificationResult, message string) ([]CommandSequence, error) {
	cdh.logger.WithFields(logrus.Fields{
		"intent":   classification.Intent,
		"entities": classification.Entities,
		"message":  message,
	}).Info("Generating connection diagnosis commands")

	// 获取目标主机
	targetHost := cdh.getTargetHost(classification.Entities)
	if targetHost == "" {
		return nil, fmt.Errorf("no target host found in entities")
	}

	// 根据诊断类型生成不同的命令序列
	diagnosisType := cdh.getDiagnosisType(classification.Entities, message)

	switch diagnosisType {
	case "ssh_authentication":
		return cdh.generateSSHAuthCommands(targetHost), nil
	case "command_execution_error":
		return cdh.generateCommandExecutionDiagnosisCommands(targetHost), nil
	case "connection_test":
		return cdh.generateConnectionTestCommands(targetHost), nil
	default:
		return cdh.generateGeneralDiagnosisCommands(targetHost), nil
	}
}

// GenerateExecutionPlan 生成执行计划
func (cdh *connectionDiagnosisHandler) GenerateExecutionPlan(commands []CommandSequence) *ExecutionPlan {
	return &ExecutionPlan{
		Strategy:      "sequential",
		Timeout:       60, // 60秒总超时
		ErrorHandling: "continue_on_error",
		Prerequisites: []string{"target_host_available"},
		Variables: map[string]string{
			"max_retries":    "3",
			"retry_interval": "5",
		},
	}
}

// GetDescription 获取处理器描述
func (cdh *connectionDiagnosisHandler) GetDescription() string {
	return "Connection diagnosis handler for SSH connectivity, authentication, and command execution issues"
}

// getTargetHost 获取目标主机
func (cdh *connectionDiagnosisHandler) getTargetHost(entities map[string]interface{}) string {
	if ip, exists := entities[EntityIPAddress]; exists {
		if ipStr, ok := ip.(string); ok {
			return ipStr
		}
	}

	if hostname, exists := entities[EntityHostname]; exists {
		if hostnameStr, ok := hostname.(string); ok {
			return hostnameStr
		}
	}

	return ""
}

// getDiagnosisType 获取诊断类型
func (cdh *connectionDiagnosisHandler) getDiagnosisType(entities map[string]interface{}, message string) string {
	if action, exists := entities["action"]; exists {
		if actionStr, ok := action.(string); ok {
			return cdh.mapActionToDiagnosisType(actionStr)
		}
	}

	// 基于消息内容推断诊断类型
	message = strings.ToLower(message)
	if strings.Contains(message, "登录") || strings.Contains(message, "认证") || strings.Contains(message, "密码") {
		return "ssh_authentication"
	}
	if strings.Contains(message, "执行命令") || strings.Contains(message, "命令报错") {
		return "command_execution_error"
	}
	if strings.Contains(message, "连接") {
		return "connection_test"
	}

	return "general_diagnosis"
}

// mapActionToDiagnosisType 映射动作到诊断类型
func (cdh *connectionDiagnosisHandler) mapActionToDiagnosisType(action string) string {
	switch action {
	case "check_login":
		return "ssh_authentication"
	case "check_command_execution":
		return "command_execution_error"
	case "check_connection":
		return "connection_test"
	default:
		return "general_diagnosis"
	}
}

// generateSSHAuthCommands 生成SSH认证诊断命令
func (cdh *connectionDiagnosisHandler) generateSSHAuthCommands(targetHost string) []CommandSequence {
	return []CommandSequence{
		{
			Step:        1,
			Command:     fmt.Sprintf("nc -zv %s 22", targetHost),
			Description: "检查SSH端口(22)连通性",
			Parameters:  map[string]string{"host": targetHost, "port": "22"},
			Timeout:     10,
			Required:    true,
			OnError:     "continue",
		},
		{
			Step:        2,
			Command:     fmt.Sprintf("ssh -o ConnectTimeout=5 -o BatchMode=yes -o PasswordAuthentication=no %s 'echo ssh_key_test'", targetHost),
			Description: "测试SSH密钥认证",
			Parameters:  map[string]string{"host": targetHost, "auth_method": "key"},
			Timeout:     10,
			Required:    false,
			OnError:     "continue",
		},
		{
			Step:        3,
			Command:     fmt.Sprintf("ssh -o ConnectTimeout=5 -o PreferredAuthentications=password %s 'echo password_test'", targetHost),
			Description: "测试SSH密码认证",
			Parameters:  map[string]string{"host": targetHost, "auth_method": "password"},
			Timeout:     15,
			Required:    false,
			OnError:     "continue",
		},
		{
			Step:        4,
			Command:     fmt.Sprintf("ssh -vvv -o ConnectTimeout=5 %s 'exit' 2>&1 | head -20", targetHost),
			Description: "获取详细SSH连接日志",
			Parameters:  map[string]string{"host": targetHost, "verbose": "true"},
			Timeout:     15,
			Required:    false,
			OnError:     "continue",
		},
	}
}

// generateCommandExecutionDiagnosisCommands 生成命令执行诊断命令
func (cdh *connectionDiagnosisHandler) generateCommandExecutionDiagnosisCommands(targetHost string) []CommandSequence {
	return []CommandSequence{
		{
			Step:        1,
			Command:     fmt.Sprintf("ssh -o ConnectTimeout=5 %s 'echo connection_test'", targetHost),
			Description: "测试基础SSH连接",
			Parameters:  map[string]string{"host": targetHost},
			Timeout:     10,
			Required:    true,
			OnError:     "stop",
		},
		{
			Step:        2,
			Command:     fmt.Sprintf("ssh %s 'whoami && pwd && date'", targetHost),
			Description: "测试基础命令执行",
			Parameters:  map[string]string{"host": targetHost},
			Timeout:     10,
			Required:    true,
			OnError:     "continue",
		},
		{
			Step:        3,
			Command:     fmt.Sprintf("ssh %s 'echo $SHELL && echo $PATH'", targetHost),
			Description: "检查Shell环境",
			Parameters:  map[string]string{"host": targetHost},
			Timeout:     10,
			Required:    false,
			OnError:     "continue",
		},
		{
			Step:        4,
			Command:     fmt.Sprintf("ssh %s 'ulimit -a'", targetHost),
			Description: "检查系统限制",
			Parameters:  map[string]string{"host": targetHost},
			Timeout:     10,
			Required:    false,
			OnError:     "continue",
		},
		{
			Step:        5,
			Command:     fmt.Sprintf("ssh %s 'ls -la /tmp && df -h'", targetHost),
			Description: "检查文件系统状态",
			Parameters:  map[string]string{"host": targetHost},
			Timeout:     15,
			Required:    false,
			OnError:     "continue",
		},
	}
}

// generateConnectionTestCommands 生成连接测试命令
func (cdh *connectionDiagnosisHandler) generateConnectionTestCommands(targetHost string) []CommandSequence {
	return []CommandSequence{
		{
			Step:        1,
			Command:     fmt.Sprintf("ping -c 4 %s", targetHost),
			Description: "测试网络连通性",
			Parameters:  map[string]string{"host": targetHost, "count": "4"},
			Timeout:     15,
			Required:    true,
			OnError:     "continue",
		},
		{
			Step:        2,
			Command:     fmt.Sprintf("nc -zv %s 22", targetHost),
			Description: "测试SSH端口连通性",
			Parameters:  map[string]string{"host": targetHost, "port": "22"},
			Timeout:     10,
			Required:    true,
			OnError:     "continue",
		},
		{
			Step:        3,
			Command:     fmt.Sprintf("ssh -o ConnectTimeout=5 -o BatchMode=yes %s 'echo connection_successful'", targetHost),
			Description: "测试SSH连接",
			Parameters:  map[string]string{"host": targetHost},
			Timeout:     10,
			Required:    false,
			OnError:     "continue",
		},
	}
}

// generateGeneralDiagnosisCommands 生成通用诊断命令
func (cdh *connectionDiagnosisHandler) generateGeneralDiagnosisCommands(targetHost string) []CommandSequence {
	return []CommandSequence{
		{
			Step:        1,
			Command:     fmt.Sprintf("ping -c 2 %s", targetHost),
			Description: "快速网络连通性测试",
			Parameters:  map[string]string{"host": targetHost, "count": "2"},
			Timeout:     10,
			Required:    true,
			OnError:     "continue",
		},
		{
			Step:        2,
			Command:     fmt.Sprintf("nc -zv %s 22", targetHost),
			Description: "SSH端口检查",
			Parameters:  map[string]string{"host": targetHost, "port": "22"},
			Timeout:     5,
			Required:    true,
			OnError:     "continue",
		},
		{
			Step:        3,
			Command:     fmt.Sprintf("ssh -o ConnectTimeout=5 %s 'uptime'", targetHost),
			Description: "SSH连接和系统状态检查",
			Parameters:  map[string]string{"host": targetHost},
			Timeout:     10,
			Required:    false,
			OnError:     "continue",
		},
	}
}
