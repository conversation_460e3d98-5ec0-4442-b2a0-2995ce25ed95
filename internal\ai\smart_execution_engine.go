package ai

import (
	"context"
	"fmt"
	"time"

	"aiops-platform/internal/workflow"

	"github.com/sirupsen/logrus"
)

// SmartExecutionEngine 智能执行引擎
type SmartExecutionEngine struct {
	deepseekService          interface{} // 简化为interface{}
	hostService              workflow.HostServiceInterface
	hostStatusHandler        *hostStatusDiagnosisHandler
	sshConnectionTestHandler *sshConnectionTestHandler
	logger                   *logrus.Logger
	config                   *SmartExecutionConfig
}

// SmartExecutionConfig 智能执行配置
type SmartExecutionConfig struct {
	AutoExecuteEnabled    bool          `json:"auto_execute_enabled"`
	MaxExecutionTime      time.Duration `json:"max_execution_time"`
	EnableDetailedLogging bool          `json:"enable_detailed_logging"`
	RetryAttempts         int           `json:"retry_attempts"`
}

// NewSmartExecutionEngine 创建智能执行引擎
func NewSmartExecutionEngine(
	deepseekService interface{}, // 简化为interface{}
	hostService workflow.HostServiceInterface,
	logger *logrus.Logger,
) *SmartExecutionEngine {
	config := &SmartExecutionConfig{
		AutoExecuteEnabled:    true,
		MaxExecutionTime:      60 * time.Second,
		EnableDetailedLogging: true,
		RetryAttempts:         2,
	}

	return &SmartExecutionEngine{
		deepseekService:          deepseekService,
		hostService:              hostService,
		hostStatusHandler:        nil, // 简化处理
		sshConnectionTestHandler: nil, // 简化处理
		logger:                   logger,
		config:                   config,
	}
}

// ExecuteIntent 智能执行意图
func (see *SmartExecutionEngine) ExecuteIntent(ctx context.Context, intent interface{}, originalMessage string) (*SmartExecutionResult, error) {
	if !see.config.AutoExecuteEnabled {
		return &SmartExecutionResult{
			Intent:      "unknown",
			Executed:    false,
			Message:     "自动执行已禁用，需要手动确认",
			Suggestions: []string{"启用自动执行功能", "手动执行相关操作"},
		}, nil
	}

	see.logger.Info("开始智能执行意图 - 简化模式")

	// 简化处理 - 返回默认结果
	return &SmartExecutionResult{
		Intent:   "simplified",
		Executed: false,
		Message:  "简化模式下不执行实际操作",
	}, nil
}

// executeHostStatusDiagnosis 执行主机状态诊断
func (see *SmartExecutionEngine) executeHostStatusDiagnosis(ctx context.Context, intent interface{}, originalMessage string) (*SmartExecutionResult, error) {
	see.logger.Info("执行主机状态诊断 - 简化模式")

	// 简化处理
	return &SmartExecutionResult{
		Intent:   "host_status_diagnosis",
		Executed: false,
		Message:  "简化模式下不执行实际诊断",
	}, nil
}

// executeSSHConnectionTest 执行SSH连接测试
func (see *SmartExecutionEngine) executeSSHConnectionTest(ctx context.Context, intent interface{}, originalMessage string) (*SmartExecutionResult, error) {
	see.logger.Info("执行SSH连接测试 - 简化模式")

	// 简化处理
	return &SmartExecutionResult{
		Intent:   "ssh_connection_test",
		Executed: false,
		Message:  "简化模式下不执行实际测试",
	}, nil
}

// executeConnectionDiagnosis 执行连接诊断
func (see *SmartExecutionEngine) executeConnectionDiagnosis(ctx context.Context, intent interface{}, originalMessage string) (*SmartExecutionResult, error) {
	see.logger.Info("执行连接诊断 - 简化模式")

	// 简化处理
	return &SmartExecutionResult{
		Intent:   "connection_diagnosis",
		Executed: false,
		Message:  "简化模式下不执行实际诊断",
	}, nil
}

// extractHostFromIntent 从意图中提取主机信息
func (see *SmartExecutionEngine) extractHostFromIntent(intent interface{}) string {
	// 简化处理 - 返回默认值
	return "127.0.0.1"
}

// buildHostStatusMessage 构建主机状态消息
func (see *SmartExecutionEngine) buildHostStatusMessage(result *HostDiagnosisResult) string {
	if result.Status == "online" {
		return fmt.Sprintf("✅ 主机 %s 状态正常，当前在线", result.HostIP)
	} else if result.Status == "offline" {
		return fmt.Sprintf("❌ 主机 %s 当前离线：%s", result.HostIP, result.Message)
	} else {
		return fmt.Sprintf("⚠️ 主机 %s 状态未知：%s", result.HostIP, result.Message)
	}
}

// buildHostStatusSuggestions 构建主机状态建议
func (see *SmartExecutionEngine) buildHostStatusSuggestions(result *HostDiagnosisResult) []string {
	if result.Status == "online" {
		return []string{"主机运行正常", "可以进行其他操作"}
	} else if result.Status == "offline" {
		return []string{
			"检查主机是否开机",
			"验证网络连接",
			"确认SSH服务状态",
			"检查防火墙设置",
		}
	} else {
		return []string{
			"先添加主机到系统中",
			"确认IP地址是否正确",
			"检查网络连通性",
		}
	}
}

// buildSSHTestMessage 构建SSH测试消息
func (see *SmartExecutionEngine) buildSSHTestMessage(result *SSHTestResult) string {
	if result.Success {
		return fmt.Sprintf("✅ SSH连接到 %s 成功", result.HostIP)
	} else {
		return fmt.Sprintf("❌ SSH连接到 %s 失败：%s", result.HostIP, result.ErrorMessage)
	}
}

// buildComprehensiveDiagnosisMessage 构建综合诊断消息
func (see *SmartExecutionEngine) buildComprehensiveDiagnosisMessage(hostResult *HostDiagnosisResult, sshResult *SSHTestResult, targetHost string) string {
	message := fmt.Sprintf("🔍 主机 %s 连接诊断结果：\n", targetHost)

	if hostResult != nil {
		message += fmt.Sprintf("• 主机状态：%s\n", hostResult.Message)
	}

	if sshResult != nil {
		if sshResult.Success {
			message += "• SSH连接：正常\n"
		} else {
			message += fmt.Sprintf("• SSH连接：失败 - %s\n", sshResult.ErrorMessage)
		}
	}

	return message
}

// buildComprehensiveSuggestions 构建综合建议
func (see *SmartExecutionEngine) buildComprehensiveSuggestions(hostResult *HostDiagnosisResult, sshResult *SSHTestResult) []string {
	suggestions := []string{}

	if hostResult != nil && hostResult.Status == "offline" {
		suggestions = append(suggestions, "主机离线，检查主机电源和网络")
	}

	if sshResult != nil && !sshResult.Success {
		suggestions = append(suggestions, sshResult.Suggestions...)
	}

	if len(suggestions) == 0 {
		suggestions = append(suggestions, "系统运行正常")
	}

	return suggestions
}

// SmartExecutionResult 智能执行结果
type SmartExecutionResult struct {
	Intent      string                 `json:"intent"`
	Executed    bool                   `json:"executed"`
	Message     string                 `json:"message"`
	Details     []string               `json:"details,omitempty"`
	Suggestions []string               `json:"suggestions"`
	Data        map[string]interface{} `json:"data,omitempty"`
}
