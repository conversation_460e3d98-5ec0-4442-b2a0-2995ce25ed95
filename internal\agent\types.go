package agent

import (
	"context"
	"time"
)

// ===== Agent核心接口定义 =====

// Agent 运维Agent接口
type Agent interface {
	// 基础信息
	GetMetadata() *AgentMetadata
	GetID() string
	GetName() string
	GetVersion() string
	GetStatus() AgentStatus

	// 生命周期管理
	Initialize(ctx context.Context, config map[string]interface{}) error
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
	HealthCheck(ctx context.Context) *HealthStatus

	// 核心执行
	Execute(ctx context.Context, request *ExecutionRequest) (*ExecutionResult, error)
	CanExecute(request *ExecutionRequest) bool
	ValidateParameters(params map[string]interface{}) error

	// 能力声明
	GetCapabilities() []Capability
	GetRequiredParameters() []Parameter
	GetOptionalParameters() []Parameter
	GetExecutionConditions() []ExecutionCondition
}

// AgentMetadata Agent元数据
type AgentMetadata struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Version     string            `json:"version"`
	Description string            `json:"description"`
	Category    AgentCategory     `json:"category"`
	Tags        []string          `json:"tags"`
	Author      string            `json:"author"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
	Config      map[string]string `json:"config"`
}

// AgentCategory Agent分类
type AgentCategory string

const (
	CategoryHostManagement    AgentCategory = "host_management"
	CategoryCommandExecution  AgentCategory = "command_execution"
	CategoryNetworkDiagnosis  AgentCategory = "network_diagnosis"
	CategorySystemMonitoring  AgentCategory = "system_monitoring"
	CategoryLogAnalysis       AgentCategory = "log_analysis"
	CategorySecurityCheck     AgentCategory = "security_check"
	CategoryBackupRestore     AgentCategory = "backup_restore"
	CategoryFileOperations    AgentCategory = "file_operations"
	CategoryServiceManagement AgentCategory = "service_management"
)

// AgentStatus Agent状态
type AgentStatus string

const (
	StatusUnknown      AgentStatus = "unknown"
	StatusInitializing AgentStatus = "initializing"
	StatusReady        AgentStatus = "ready"
	StatusRunning      AgentStatus = "running"
	StatusStopped      AgentStatus = "stopped"
	StatusError        AgentStatus = "error"
	StatusMaintenance  AgentStatus = "maintenance"
)

// Capability Agent能力定义
type Capability struct {
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Type        CapabilityType    `json:"type"`
	Parameters  []Parameter       `json:"parameters"`
	Examples    []string          `json:"examples"`
	Metadata    map[string]string `json:"metadata"`
}

// CapabilityType 能力类型
type CapabilityType string

const (
	CapabilityTypeQuery    CapabilityType = "query"    // 查询类操作
	CapabilityTypeAction   CapabilityType = "action"   // 执行类操作
	CapabilityTypeAnalysis CapabilityType = "analysis" // 分析类操作
	CapabilityTypeMonitor  CapabilityType = "monitor"  // 监控类操作
)

// Parameter 参数定义
type Parameter struct {
	Name         string      `json:"name"`
	Type         string      `json:"type"`
	Description  string      `json:"description"`
	Required     bool        `json:"required"`
	DefaultValue interface{} `json:"default_value,omitempty"`
	Validation   *Validation `json:"validation,omitempty"`
	Examples     []string    `json:"examples,omitempty"`
}

// Validation 参数验证规则
type Validation struct {
	Pattern string   `json:"pattern,omitempty"` // 正则表达式
	MinLen  int      `json:"min_len,omitempty"` // 最小长度
	MaxLen  int      `json:"max_len,omitempty"` // 最大长度
	Options []string `json:"options,omitempty"` // 可选值
	Range   *Range   `json:"range,omitempty"`   // 数值范围
}

// Range 数值范围
type Range struct {
	Min float64 `json:"min"`
	Max float64 `json:"max"`
}

// ExecutionCondition 执行条件
type ExecutionCondition struct {
	Type        ConditionType `json:"type"`
	Description string        `json:"description"`
	Expression  string        `json:"expression"`
	Required    bool          `json:"required"`
}

// ConditionType 条件类型
type ConditionType string

const (
	ConditionTypeHostAvailable ConditionType = "host_available"
	ConditionTypePermission    ConditionType = "permission"
	ConditionTypeResourceLimit ConditionType = "resource_limit"
	ConditionTypeDependency    ConditionType = "dependency"
	ConditionTypeEnvironment   ConditionType = "environment"
)

// ===== 执行相关类型 =====

// ExecutionRequest Agent执行请求
type ExecutionRequest struct {
	ID          string                 `json:"id"`
	AgentID     string                 `json:"agent_id"`
	Capability  string                 `json:"capability"`
	Parameters  map[string]interface{} `json:"parameters"`
	Context     *ExecutionContext      `json:"context"`
	Options     *ExecutionOptions      `json:"options"`
	CreatedAt   time.Time              `json:"created_at"`
	RequestedBy string                 `json:"requested_by"`
}

// ExecutionContext 执行上下文
type ExecutionContext struct {
	SessionID string                 `json:"session_id"`
	UserID    int64                  `json:"user_id"`
	TraceID   string                 `json:"trace_id"`
	ParentID  string                 `json:"parent_id,omitempty"`
	Variables map[string]interface{} `json:"variables"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// ExecutionOptions 执行选项
type ExecutionOptions struct {
	Timeout    time.Duration `json:"timeout"`
	Async      bool          `json:"async"`
	RetryCount int           `json:"retry_count"`
	RetryDelay time.Duration `json:"retry_delay"`
	DryRun     bool          `json:"dry_run"`
	Verbose    bool          `json:"verbose"`
}

// ExecutionResult Agent执行结果
type ExecutionResult struct {
	ID          string                 `json:"id"`
	AgentID     string                 `json:"agent_id"`
	RequestID   string                 `json:"request_id"`
	Success     bool                   `json:"success"`
	Data        interface{}            `json:"data,omitempty"`
	Error       string                 `json:"error,omitempty"`
	Metadata    map[string]interface{} `json:"metadata"`
	Metrics     *ExecutionMetrics      `json:"metrics"`
	CreatedAt   time.Time              `json:"created_at"`
	CompletedAt time.Time              `json:"completed_at"`
}

// ExecutionMetrics 执行指标
type ExecutionMetrics struct {
	Duration         time.Duration `json:"duration"`
	CPUUsage         float64       `json:"cpu_usage"`
	MemoryUsage      int64         `json:"memory_usage"`
	NetworkIO        int64         `json:"network_io"`
	DiskIO           int64         `json:"disk_io"`
	CommandsExecuted int           `json:"commands_executed"`
}

// ===== 健康检查相关 =====

// HealthStatus 健康状态
type HealthStatus struct {
	Status     HealthStatusType       `json:"status"`
	Message    string                 `json:"message"`
	Details    map[string]interface{} `json:"details"`
	LastCheck  time.Time              `json:"last_check"`
	CheckCount int64                  `json:"check_count"`
	Uptime     time.Duration          `json:"uptime"`
}

// HealthStatusType 健康状态类型
type HealthStatusType string

const (
	HealthStatusHealthy   HealthStatusType = "healthy"
	HealthStatusUnhealthy HealthStatusType = "unhealthy"
	HealthStatusDegraded  HealthStatusType = "degraded"
	HealthStatusUnknown   HealthStatusType = "unknown"
)

// ===== Agent注册相关 =====

// AgentRegistration Agent注册信息
type AgentRegistration struct {
	Metadata     *AgentMetadata       `json:"metadata"`
	Capabilities []Capability         `json:"capabilities"`
	Parameters   []Parameter          `json:"parameters"`
	Conditions   []ExecutionCondition `json:"conditions"`
	Endpoint     string               `json:"endpoint"`
	RegisteredAt time.Time            `json:"registered_at"`
	LastSeen     time.Time            `json:"last_seen"`
	Status       AgentStatus          `json:"status"`
}

// ===== 工具函数 =====

// NewExecutionRequest 创建执行请求
func NewExecutionRequest(agentID, capability string, params map[string]interface{}) *ExecutionRequest {
	return &ExecutionRequest{
		ID:         generateID(),
		AgentID:    agentID,
		Capability: capability,
		Parameters: params,
		CreatedAt:  time.Now(),
		Context:    &ExecutionContext{},
		Options:    &ExecutionOptions{},
	}
}

// NewExecutionResult 创建执行结果
func NewExecutionResult(agentID, requestID string) *ExecutionResult {
	return &ExecutionResult{
		ID:        generateID(),
		AgentID:   agentID,
		RequestID: requestID,
		Metadata:  make(map[string]interface{}),
		Metrics:   &ExecutionMetrics{},
		CreatedAt: time.Now(),
	}
}

// generateID 生成唯一ID
func generateID() string {
	return time.Now().Format("20060102150405") + "_" + randomString(8)
}

// randomString 生成随机字符串
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}

// IsValidAgentID 验证Agent ID
func IsValidAgentID(id string) bool {
	return len(id) > 0 && len(id) <= 64
}

// IsValidCapability 验证能力名称
func IsValidCapability(capability string) bool {
	return len(capability) > 0 && len(capability) <= 128
}

// GetCategoryDescription 获取分类描述
func GetCategoryDescription(category AgentCategory) string {
	descriptions := map[AgentCategory]string{
		CategoryHostManagement:    "主机管理相关操作",
		CategoryCommandExecution:  "命令执行相关操作",
		CategoryNetworkDiagnosis:  "网络诊断相关操作",
		CategorySystemMonitoring:  "系统监控相关操作",
		CategoryLogAnalysis:       "日志分析相关操作",
		CategorySecurityCheck:     "安全检查相关操作",
		CategoryBackupRestore:     "备份恢复相关操作",
		CategoryFileOperations:    "文件操作相关操作",
		CategoryServiceManagement: "服务管理相关操作",
	}
	return descriptions[category]
}
