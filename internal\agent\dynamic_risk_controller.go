package agent

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// DynamicRiskController 动态风险控制器
type DynamicRiskController struct {
	config              *DynamicRiskConfig
	logger              *logrus.Logger
	riskEngine          *RiskAssessmentEngine
	riskPolicies        map[string]*RiskPolicy
	riskThresholds      *DynamicThresholds
	adaptiveController  *AdaptiveController
	realTimeMonitor     *RealTimeRiskMonitor
	alertManager        *RiskAlertManager
	auditLogger         *RiskAuditLogger
	emergencyController *EmergencyController
	mutex               sync.RWMutex
	isRunning           bool
	stopChan            chan struct{}
}

// DynamicRiskConfig 动态风险控制配置
type DynamicRiskConfig struct {
	EnableAdaptiveControl  bool          `json:"enable_adaptive_control"`
	EnableRealTimeMonitor  bool          `json:"enable_real_time_monitor"`
	EnableEmergencyControl bool          `json:"enable_emergency_control"`
	EnableRiskAudit        bool          `json:"enable_risk_audit"`
	AdaptationInterval     time.Duration `json:"adaptation_interval"`
	MonitoringInterval     time.Duration `json:"monitoring_interval"`
	AlertThreshold         float64       `json:"alert_threshold"`
	EmergencyThreshold     float64       `json:"emergency_threshold"`
	MaxRiskScore           float64       `json:"max_risk_score"`
	AutoBlockEnabled       bool          `json:"auto_block_enabled"`
	RequireApproval        bool          `json:"require_approval"`
	ApprovalTimeout        time.Duration `json:"approval_timeout"`
}

// RiskPolicy 风险策略
type RiskPolicy struct {
	ID            string                 `json:"id"`
	Name          string                 `json:"name"`
	Description   string                 `json:"description"`
	RiskLevel     string                 `json:"risk_level"`
	Action        string                 `json:"action"` // allow, block, require_approval, sandbox
	Conditions    []*PolicyCondition     `json:"conditions"`
	Restrictions  *PolicyRestrictions    `json:"restrictions"`
	Notifications *PolicyNotifications   `json:"notifications"`
	Enabled       bool                   `json:"enabled"`
	Priority      int                    `json:"priority"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// PolicyCondition 策略条件
type PolicyCondition struct {
	Type     string      `json:"type"` // risk_score, user_role, environment, time, command
	Field    string      `json:"field"`
	Operator string      `json:"operator"` // gt, lt, eq, ne, in, contains
	Value    interface{} `json:"value"`
	Weight   float64     `json:"weight"`
}

// PolicyRestrictions 策略限制
type PolicyRestrictions struct {
	MaxExecutionsPerHour int           `json:"max_executions_per_hour"`
	MaxExecutionsPerDay  int           `json:"max_executions_per_day"`
	AllowedTimeWindows   []TimeWindow  `json:"allowed_time_windows"`
	RequiredApprovers    []string      `json:"required_approvers"`
	MandatorySandbox     bool          `json:"mandatory_sandbox"`
	RequiredBackup       bool          `json:"required_backup"`
	CooldownPeriod       time.Duration `json:"cooldown_period"`
}

// PolicyNotifications 策略通知
type PolicyNotifications struct {
	NotifyOnExecution bool     `json:"notify_on_execution"`
	NotifyOnBlock     bool     `json:"notify_on_block"`
	NotifyOnApproval  bool     `json:"notify_on_approval"`
	Recipients        []string `json:"recipients"`
	Channels          []string `json:"channels"` // email, slack, webhook
}

// TimeWindow 时间窗口
type TimeWindow struct {
	StartTime string `json:"start_time"` // HH:MM
	EndTime   string `json:"end_time"`   // HH:MM
	Days      []int  `json:"days"`       // 0=Sunday, 1=Monday, ...
}

// DynamicThresholds 动态阈值
type DynamicThresholds struct {
	LowThreshold      float64           `json:"low_threshold"`
	MediumThreshold   float64           `json:"medium_threshold"`
	HighThreshold     float64           `json:"high_threshold"`
	CriticalThreshold float64           `json:"critical_threshold"`
	LastUpdated       time.Time         `json:"last_updated"`
	AdaptationHistory []ThresholdChange `json:"adaptation_history"`
}

// ThresholdChange 阈值变更记录
type ThresholdChange struct {
	Timestamp   time.Time `json:"timestamp"`
	OldValue    float64   `json:"old_value"`
	NewValue    float64   `json:"new_value"`
	Reason      string    `json:"reason"`
	TriggeredBy string    `json:"triggered_by"`
}

// AdaptiveController 自适应控制器
type AdaptiveController struct {
	logger              *logrus.Logger
	learningRate        float64
	adaptationThreshold float64
	riskHistory         []RiskEvent
	performanceMetrics  *PerformanceMetrics
	mutex               sync.RWMutex
}

// RiskEvent 风险事件
type RiskEvent struct {
	Timestamp   time.Time `json:"timestamp"`
	Command     string    `json:"command"`
	RiskScore   float64   `json:"risk_score"`
	Action      string    `json:"action"`
	Outcome     string    `json:"outcome"` // success, failure, blocked
	UserID      int64     `json:"user_id"`
	SessionID   string    `json:"session_id"`
	Environment string    `json:"environment"`
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	TotalCommands      int64     `json:"total_commands"`
	BlockedCommands    int64     `json:"blocked_commands"`
	SuccessfulCommands int64     `json:"successful_commands"`
	FailedCommands     int64     `json:"failed_commands"`
	FalsePositives     int64     `json:"false_positives"`
	FalseNegatives     int64     `json:"false_negatives"`
	AverageRiskScore   float64   `json:"average_risk_score"`
	BlockingAccuracy   float64   `json:"blocking_accuracy"`
	LastUpdated        time.Time `json:"last_updated"`
}

// RealTimeRiskMonitor 实时风险监控器
type RealTimeRiskMonitor struct {
	logger          *logrus.Logger
	activeRisks     map[string]*ActiveRisk
	riskTrends      *RiskTrends
	alertThresholds *AlertThresholds
	mutex           sync.RWMutex
}

// ActiveRisk 活跃风险
type ActiveRisk struct {
	ID              string    `json:"id"`
	Type            string    `json:"type"`
	Severity        string    `json:"severity"`
	Description     string    `json:"description"`
	FirstDetected   time.Time `json:"first_detected"`
	LastSeen        time.Time `json:"last_seen"`
	Frequency       int       `json:"frequency"`
	AffectedSystems []string  `json:"affected_systems"`
	Mitigation      string    `json:"mitigation"`
	Status          string    `json:"status"` // active, mitigated, resolved
}

// RiskTrends 风险趋势
type RiskTrends struct {
	HourlyRiskScores []float64 `json:"hourly_risk_scores"`
	DailyRiskScores  []float64 `json:"daily_risk_scores"`
	WeeklyRiskScores []float64 `json:"weekly_risk_scores"`
	TrendDirection   string    `json:"trend_direction"` // increasing, decreasing, stable
	PredictedRisk    float64   `json:"predicted_risk"`
	ConfidenceLevel  float64   `json:"confidence_level"`
	LastUpdated      time.Time `json:"last_updated"`
}

// AlertThresholds 告警阈值
type AlertThresholds struct {
	RiskScoreThreshold float64 `json:"risk_score_threshold"`
	FrequencyThreshold int     `json:"frequency_threshold"`
	TrendThreshold     float64 `json:"trend_threshold"`
	AnomalyThreshold   float64 `json:"anomaly_threshold"`
}

// RiskAlertManager 风险告警管理器
type RiskAlertManager struct {
	logger        *logrus.Logger
	alertChannels map[string]AlertChannel
	alertRules    []*AlertRule
	alertHistory  []*Alert
	mutex         sync.RWMutex
}

// AlertChannel 告警通道
type AlertChannel interface {
	SendAlert(alert *Alert) error
	GetChannelType() string
	IsEnabled() bool
}

// AlertRule 告警规则
type AlertRule struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Condition string                 `json:"condition"`
	Threshold float64                `json:"threshold"`
	Severity  string                 `json:"severity"`
	Channels  []string               `json:"channels"`
	Enabled   bool                   `json:"enabled"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// Alert 告警
type Alert struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Severity    string                 `json:"severity"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Timestamp   time.Time              `json:"timestamp"`
	Source      string                 `json:"source"`
	Tags        []string               `json:"tags"`
	Metadata    map[string]interface{} `json:"metadata"`
	Status      string                 `json:"status"` // new, acknowledged, resolved
}

// RiskAuditLogger 风险审计日志器
type RiskAuditLogger struct {
	logger    *logrus.Logger
	auditLogs []*AuditLog
	mutex     sync.RWMutex
}

// AuditLog 审计日志
type AuditLog struct {
	ID        string                 `json:"id"`
	Timestamp time.Time              `json:"timestamp"`
	EventType string                 `json:"event_type"`
	UserID    int64                  `json:"user_id"`
	SessionID string                 `json:"session_id"`
	Command   string                 `json:"command"`
	RiskScore float64                `json:"risk_score"`
	Action    string                 `json:"action"`
	Outcome   string                 `json:"outcome"`
	Reason    string                 `json:"reason"`
	IPAddress string                 `json:"ip_address"`
	UserAgent string                 `json:"user_agent"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// EmergencyController 应急控制器
type EmergencyController struct {
	logger           *logrus.Logger
	emergencyMode    bool
	emergencyTrigger *EmergencyTrigger
	emergencyActions *EmergencyActions
	mutex            sync.RWMutex
}

// EmergencyTrigger 应急触发器
type EmergencyTrigger struct {
	RiskScoreThreshold  float64 `json:"risk_score_threshold"`
	FrequencyThreshold  int     `json:"frequency_threshold"`
	TimeWindowMinutes   int     `json:"time_window_minutes"`
	ConsecutiveFailures int     `json:"consecutive_failures"`
	SystemLoadThreshold float64 `json:"system_load_threshold"`
}

// EmergencyActions 应急行动
type EmergencyActions struct {
	BlockAllCommands     bool     `json:"block_all_commands"`
	NotifyAdministrators bool     `json:"notify_administrators"`
	EnableSandboxMode    bool     `json:"enable_sandbox_mode"`
	ReduceRiskThresholds bool     `json:"reduce_risk_thresholds"`
	LogAllActivities     bool     `json:"log_all_activities"`
	AlertChannels        []string `json:"alert_channels"`
}

// RiskControlDecision 风险控制决策
type RiskControlDecision struct {
	Action          string                 `json:"action"` // allow, block, require_approval, sandbox
	Reason          string                 `json:"reason"`
	RiskScore       float64                `json:"risk_score"`
	PolicyID        string                 `json:"policy_id"`
	RequiredActions []string               `json:"required_actions"`
	Restrictions    *PolicyRestrictions    `json:"restrictions"`
	Metadata        map[string]interface{} `json:"metadata"`
	Timestamp       time.Time              `json:"timestamp"`
}

// NewDynamicRiskController 创建动态风险控制器
func NewDynamicRiskController(config *DynamicRiskConfig, riskEngine *RiskAssessmentEngine, logger *logrus.Logger) *DynamicRiskController {
	if config == nil {
		config = DefaultDynamicRiskConfig()
	}

	controller := &DynamicRiskController{
		config:         config,
		logger:         logger,
		riskEngine:     riskEngine,
		riskPolicies:   make(map[string]*RiskPolicy),
		riskThresholds: NewDynamicThresholds(),
		stopChan:       make(chan struct{}),
		isRunning:      false,
	}

	// 初始化子组件
	if config.EnableAdaptiveControl {
		controller.adaptiveController = NewAdaptiveController(logger)
	}

	if config.EnableRealTimeMonitor {
		controller.realTimeMonitor = NewRealTimeRiskMonitor(logger)
	}

	controller.alertManager = NewRiskAlertManager(logger)

	if config.EnableRiskAudit {
		controller.auditLogger = NewRiskAuditLogger(logger)
	}

	if config.EnableEmergencyControl {
		controller.emergencyController = NewEmergencyController(logger)
	}

	// 加载默认策略
	controller.loadDefaultPolicies()

	return controller
}

// DefaultDynamicRiskConfig 默认动态风险控制配置
func DefaultDynamicRiskConfig() *DynamicRiskConfig {
	return &DynamicRiskConfig{
		EnableAdaptiveControl:  true,
		EnableRealTimeMonitor:  true,
		EnableEmergencyControl: true,
		EnableRiskAudit:        true,
		AdaptationInterval:     15 * time.Minute,
		MonitoringInterval:     1 * time.Minute,
		AlertThreshold:         0.8,
		EmergencyThreshold:     0.95,
		MaxRiskScore:           1.0,
		AutoBlockEnabled:       true,
		RequireApproval:        true,
		ApprovalTimeout:        30 * time.Minute,
	}
}

// Start 启动动态风险控制器
func (drc *DynamicRiskController) Start(ctx context.Context) error {
	drc.mutex.Lock()
	defer drc.mutex.Unlock()

	if drc.isRunning {
		return fmt.Errorf("dynamic risk controller is already running")
	}

	drc.logger.Info("Starting dynamic risk controller")

	// 启动自适应控制器
	if drc.config.EnableAdaptiveControl && drc.adaptiveController != nil {
		go drc.adaptiveControlRoutine(ctx)
	}

	// 启动实时监控器
	if drc.config.EnableRealTimeMonitor && drc.realTimeMonitor != nil {
		go drc.realTimeMonitorRoutine(ctx)
	}

	// 启动应急控制器
	if drc.config.EnableEmergencyControl && drc.emergencyController != nil {
		go drc.emergencyControlRoutine(ctx)
	}

	drc.isRunning = true
	drc.logger.Info("Dynamic risk controller started successfully")

	return nil
}

// Stop 停止动态风险控制器
func (drc *DynamicRiskController) Stop(ctx context.Context) error {
	drc.mutex.Lock()
	defer drc.mutex.Unlock()

	if !drc.isRunning {
		return nil
	}

	drc.logger.Info("Stopping dynamic risk controller")

	close(drc.stopChan)
	drc.isRunning = false

	drc.logger.Info("Dynamic risk controller stopped")
	return nil
}

// EvaluateRiskControl 评估风险控制决策
func (drc *DynamicRiskController) EvaluateRiskControl(ctx context.Context, assessment *RiskAssessment, userID int64, sessionID string) (*RiskControlDecision, error) {
	drc.mutex.RLock()
	defer drc.mutex.RUnlock()

	if !drc.isRunning {
		return nil, fmt.Errorf("dynamic risk controller is not running")
	}

	// 创建控制决策
	decision := &RiskControlDecision{
		RiskScore:       assessment.RiskScore,
		RequiredActions: make([]string, 0),
		Metadata:        make(map[string]interface{}),
		Timestamp:       time.Now(),
	}

	// 检查应急模式
	if drc.emergencyController != nil && drc.emergencyController.IsEmergencyMode() {
		decision.Action = "block"
		decision.Reason = "系统处于应急模式，所有命令被阻止"
		decision.PolicyID = "emergency_mode"
		return decision, nil
	}

	// 应用风险策略
	policy, err := drc.findApplicablePolicy(assessment, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to find applicable policy: %w", err)
	}

	if policy != nil {
		decision.PolicyID = policy.ID
		decision.Action = policy.Action
		decision.Reason = fmt.Sprintf("应用策略: %s", policy.Name)
		decision.Restrictions = policy.Restrictions

		// 检查策略条件
		if !drc.evaluatePolicyConditions(policy, assessment, userID) {
			decision.Action = "block"
			decision.Reason = "不满足策略条件"
		}
	} else {
		// 使用默认策略
		decision = drc.applyDefaultPolicy(assessment)
	}

	// 记录风险事件
	if drc.config.EnableRiskAudit && drc.auditLogger != nil {
		riskEvent := &RiskEvent{
			Timestamp: time.Now(),
			Command:   assessment.Command,
			RiskScore: assessment.RiskScore,
			Action:    decision.Action,
			UserID:    userID,
			SessionID: sessionID,
		}
		drc.auditLogger.LogRiskEvent(riskEvent)
	}

	// 更新实时监控
	if drc.config.EnableRealTimeMonitor && drc.realTimeMonitor != nil {
		drc.realTimeMonitor.UpdateRiskMetrics(assessment, decision)
	}

	// 发送告警
	if assessment.RiskScore >= drc.config.AlertThreshold {
		drc.sendRiskAlert(assessment, decision, userID)
	}

	drc.logger.WithFields(logrus.Fields{
		"command":    assessment.Command,
		"risk_score": assessment.RiskScore,
		"action":     decision.Action,
		"policy_id":  decision.PolicyID,
		"user_id":    userID,
	}).Info("Risk control decision made")

	return decision, nil
}

// findApplicablePolicy 查找适用的策略
func (drc *DynamicRiskController) findApplicablePolicy(assessment *RiskAssessment, userID int64) (*RiskPolicy, error) {
	var applicablePolicies []*RiskPolicy

	for _, policy := range drc.riskPolicies {
		if !policy.Enabled {
			continue
		}

		// 检查风险等级匹配
		if policy.RiskLevel != "" && policy.RiskLevel != assessment.RiskLevel {
			continue
		}

		// 检查策略条件
		if drc.evaluatePolicyConditions(policy, assessment, userID) {
			applicablePolicies = append(applicablePolicies, policy)
		}
	}

	// 按优先级排序，返回最高优先级的策略
	if len(applicablePolicies) > 0 {
		highestPriority := applicablePolicies[0]
		for _, policy := range applicablePolicies[1:] {
			if policy.Priority > highestPriority.Priority {
				highestPriority = policy
			}
		}
		return highestPriority, nil
	}

	return nil, nil
}

// evaluatePolicyConditions 评估策略条件
func (drc *DynamicRiskController) evaluatePolicyConditions(policy *RiskPolicy, assessment *RiskAssessment, userID int64) bool {
	for _, condition := range policy.Conditions {
		if !drc.evaluateCondition(condition, assessment, userID) {
			return false
		}
	}
	return true
}

// evaluateCondition 评估单个条件
func (drc *DynamicRiskController) evaluateCondition(condition *PolicyCondition, assessment *RiskAssessment, userID int64) bool {
	switch condition.Type {
	case "risk_score":
		return drc.compareValues(assessment.RiskScore, condition.Operator, condition.Value)
	case "user_role":
		// 简化实现，基于用户ID判断角色
		userRole := "user"
		if userID == 1 {
			userRole = "admin"
		}
		return drc.compareValues(userRole, condition.Operator, condition.Value)
	case "environment":
		// 简化实现，假设为生产环境
		return drc.compareValues("production", condition.Operator, condition.Value)
	case "time":
		now := time.Now()
		return drc.compareValues(now.Hour(), condition.Operator, condition.Value)
	case "command":
		return drc.compareValues(assessment.Command, condition.Operator, condition.Value)
	default:
		return true
	}
}

// compareValues 比较值
func (drc *DynamicRiskController) compareValues(actual interface{}, operator string, expected interface{}) bool {
	switch operator {
	case "eq":
		return actual == expected
	case "ne":
		return actual != expected
	case "gt":
		if actualFloat, ok := actual.(float64); ok {
			if expectedFloat, ok := expected.(float64); ok {
				return actualFloat > expectedFloat
			}
		}
		if actualInt, ok := actual.(int); ok {
			if expectedInt, ok := expected.(int); ok {
				return actualInt > expectedInt
			}
		}
	case "lt":
		if actualFloat, ok := actual.(float64); ok {
			if expectedFloat, ok := expected.(float64); ok {
				return actualFloat < expectedFloat
			}
		}
		if actualInt, ok := actual.(int); ok {
			if expectedInt, ok := expected.(int); ok {
				return actualInt < expectedInt
			}
		}
	case "contains":
		if actualStr, ok := actual.(string); ok {
			if expectedStr, ok := expected.(string); ok {
				return strings.Contains(actualStr, expectedStr)
			}
		}
	}
	return false
}

// applyDefaultPolicy 应用默认策略
func (drc *DynamicRiskController) applyDefaultPolicy(assessment *RiskAssessment) *RiskControlDecision {
	decision := &RiskControlDecision{
		RiskScore:       assessment.RiskScore,
		RequiredActions: make([]string, 0),
		Metadata:        make(map[string]interface{}),
		Timestamp:       time.Now(),
		PolicyID:        "default",
	}

	switch assessment.RiskLevel {
	case "critical":
		decision.Action = "block"
		decision.Reason = "极高风险命令，自动阻止执行"
		decision.RequiredActions = append(decision.RequiredActions, "管理员审批", "完整备份", "测试验证")
	case "high":
		if drc.config.RequireApproval {
			decision.Action = "require_approval"
			decision.Reason = "高风险命令，需要审批"
			decision.RequiredActions = append(decision.RequiredActions, "上级审批", "风险评估")
		} else {
			decision.Action = "sandbox"
			decision.Reason = "高风险命令，在沙箱中执行"
		}
	case "medium":
		decision.Action = "sandbox"
		decision.Reason = "中等风险命令，建议在沙箱中执行"
		decision.RequiredActions = append(decision.RequiredActions, "确认参数", "准备回滚")
	case "low":
		decision.Action = "allow"
		decision.Reason = "低风险命令，允许执行"
	default:
		decision.Action = "allow"
		decision.Reason = "未知风险等级，默认允许"
	}

	return decision
}

// 辅助方法和子组件初始化

// NewDynamicThresholds 创建动态阈值
func NewDynamicThresholds() *DynamicThresholds {
	return &DynamicThresholds{
		LowThreshold:      0.3,
		MediumThreshold:   0.7,
		HighThreshold:     0.9,
		CriticalThreshold: 1.0,
		LastUpdated:       time.Now(),
		AdaptationHistory: make([]ThresholdChange, 0),
	}
}

// NewAdaptiveController 创建自适应控制器
func NewAdaptiveController(logger *logrus.Logger) *AdaptiveController {
	return &AdaptiveController{
		logger:              logger,
		learningRate:        0.1,
		adaptationThreshold: 0.05,
		riskHistory:         make([]RiskEvent, 0),
		performanceMetrics: &PerformanceMetrics{
			LastUpdated: time.Now(),
		},
	}
}

// NewRealTimeRiskMonitor 创建实时风险监控器
func NewRealTimeRiskMonitor(logger *logrus.Logger) *RealTimeRiskMonitor {
	return &RealTimeRiskMonitor{
		logger:      logger,
		activeRisks: make(map[string]*ActiveRisk),
		riskTrends: &RiskTrends{
			HourlyRiskScores: make([]float64, 24),
			DailyRiskScores:  make([]float64, 7),
			WeeklyRiskScores: make([]float64, 4),
			TrendDirection:   "stable",
			LastUpdated:      time.Now(),
		},
		alertThresholds: &AlertThresholds{
			RiskScoreThreshold: 0.8,
			FrequencyThreshold: 10,
			TrendThreshold:     0.1,
			AnomalyThreshold:   0.2,
		},
	}
}

// NewRiskAlertManager 创建风险告警管理器
func NewRiskAlertManager(logger *logrus.Logger) *RiskAlertManager {
	return &RiskAlertManager{
		logger:        logger,
		alertChannels: make(map[string]AlertChannel),
		alertRules:    make([]*AlertRule, 0),
		alertHistory:  make([]*Alert, 0),
	}
}

// NewRiskAuditLogger 创建风险审计日志器
func NewRiskAuditLogger(logger *logrus.Logger) *RiskAuditLogger {
	return &RiskAuditLogger{
		logger:    logger,
		auditLogs: make([]*AuditLog, 0),
	}
}

// NewEmergencyController 创建应急控制器
func NewEmergencyController(logger *logrus.Logger) *EmergencyController {
	return &EmergencyController{
		logger:        logger,
		emergencyMode: false,
		emergencyTrigger: &EmergencyTrigger{
			RiskScoreThreshold:  0.95,
			FrequencyThreshold:  20,
			TimeWindowMinutes:   5,
			ConsecutiveFailures: 5,
			SystemLoadThreshold: 90.0,
		},
		emergencyActions: &EmergencyActions{
			BlockAllCommands:     true,
			NotifyAdministrators: true,
			EnableSandboxMode:    true,
			ReduceRiskThresholds: true,
			LogAllActivities:     true,
			AlertChannels:        []string{"email", "slack"},
		},
	}
}

// 控制器协程方法

// adaptiveControlRoutine 自适应控制协程
func (drc *DynamicRiskController) adaptiveControlRoutine(ctx context.Context) {
	ticker := time.NewTicker(drc.config.AdaptationInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-drc.stopChan:
			return
		case <-ticker.C:
			drc.performAdaptiveControl()
		}
	}
}

// realTimeMonitorRoutine 实时监控协程
func (drc *DynamicRiskController) realTimeMonitorRoutine(ctx context.Context) {
	ticker := time.NewTicker(drc.config.MonitoringInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-drc.stopChan:
			return
		case <-ticker.C:
			drc.performRealTimeMonitoring()
		}
	}
}

// emergencyControlRoutine 应急控制协程
func (drc *DynamicRiskController) emergencyControlRoutine(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-drc.stopChan:
			return
		case <-ticker.C:
			drc.checkEmergencyConditions()
		}
	}
}

// performAdaptiveControl 执行自适应控制
func (drc *DynamicRiskController) performAdaptiveControl() {
	if drc.adaptiveController == nil {
		return
	}

	// 分析性能指标
	metrics := drc.adaptiveController.performanceMetrics

	// 调整阈值
	if metrics.BlockingAccuracy < 0.8 {
		// 准确率低，调整阈值
		drc.adjustThresholds("low_accuracy")
	}

	if metrics.FalsePositives > int64(float64(metrics.TotalCommands)*0.1) {
		// 误报率高，放宽阈值
		drc.adjustThresholds("high_false_positive")
	}

	drc.logger.Info("Adaptive control performed")
}

// performRealTimeMonitoring 执行实时监控
func (drc *DynamicRiskController) performRealTimeMonitoring() {
	if drc.realTimeMonitor == nil {
		return
	}

	// 更新风险趋势
	drc.realTimeMonitor.updateRiskTrends()

	// 检查异常
	drc.realTimeMonitor.detectAnomalies()

	drc.logger.Debug("Real-time monitoring performed")
}

// checkEmergencyConditions 检查应急条件
func (drc *DynamicRiskController) checkEmergencyConditions() {
	if drc.emergencyController == nil {
		return
	}

	// 检查是否需要触发应急模式
	shouldTrigger := drc.shouldTriggerEmergency()

	if shouldTrigger && !drc.emergencyController.emergencyMode {
		drc.emergencyController.TriggerEmergency("High risk activity detected")
		drc.logger.Warn("Emergency mode triggered")
	} else if !shouldTrigger && drc.emergencyController.emergencyMode {
		drc.emergencyController.ClearEmergency()
		drc.logger.Info("Emergency mode cleared")
	}
}

// shouldTriggerEmergency 判断是否应该触发应急模式
func (drc *DynamicRiskController) shouldTriggerEmergency() bool {
	// 简化实现：基于配置的阈值判断
	if drc.realTimeMonitor != nil {
		avgRisk := drc.calculateAverageRiskScore()
		if avgRisk >= drc.config.EmergencyThreshold {
			return true
		}
	}

	return false
}

// calculateAverageRiskScore 计算平均风险分数
func (drc *DynamicRiskController) calculateAverageRiskScore() float64 {
	if drc.adaptiveController == nil || len(drc.adaptiveController.riskHistory) == 0 {
		return 0.0
	}

	total := 0.0
	count := 0

	// 计算最近1小时的平均风险分数
	oneHourAgo := time.Now().Add(-1 * time.Hour)
	for _, event := range drc.adaptiveController.riskHistory {
		if event.Timestamp.After(oneHourAgo) {
			total += event.RiskScore
			count++
		}
	}

	if count == 0 {
		return 0.0
	}

	return total / float64(count)
}

// adjustThresholds 调整阈值
func (drc *DynamicRiskController) adjustThresholds(reason string) {
	oldThreshold := drc.riskThresholds.HighThreshold

	switch reason {
	case "low_accuracy":
		// 准确率低，降低阈值使其更敏感
		drc.riskThresholds.HighThreshold *= 0.95
	case "high_false_positive":
		// 误报率高，提高阈值使其不那么敏感
		drc.riskThresholds.HighThreshold *= 1.05
	}

	// 确保阈值在合理范围内
	if drc.riskThresholds.HighThreshold < 0.5 {
		drc.riskThresholds.HighThreshold = 0.5
	} else if drc.riskThresholds.HighThreshold > 0.95 {
		drc.riskThresholds.HighThreshold = 0.95
	}

	// 记录阈值变更
	change := ThresholdChange{
		Timestamp:   time.Now(),
		OldValue:    oldThreshold,
		NewValue:    drc.riskThresholds.HighThreshold,
		Reason:      reason,
		TriggeredBy: "adaptive_controller",
	}

	drc.riskThresholds.AdaptationHistory = append(drc.riskThresholds.AdaptationHistory, change)
	drc.riskThresholds.LastUpdated = time.Now()

	drc.logger.WithFields(logrus.Fields{
		"old_threshold": oldThreshold,
		"new_threshold": drc.riskThresholds.HighThreshold,
		"reason":        reason,
	}).Info("Risk threshold adjusted")
}

// 子组件方法

// IsEmergencyMode 检查是否处于应急模式
func (ec *EmergencyController) IsEmergencyMode() bool {
	ec.mutex.RLock()
	defer ec.mutex.RUnlock()
	return ec.emergencyMode
}

// TriggerEmergency 触发应急模式
func (ec *EmergencyController) TriggerEmergency(reason string) {
	ec.mutex.Lock()
	defer ec.mutex.Unlock()

	if !ec.emergencyMode {
		ec.emergencyMode = true
		ec.logger.WithField("reason", reason).Warn("Emergency mode triggered")

		// 执行应急行动
		if ec.emergencyActions.NotifyAdministrators {
			ec.logger.Info("Notifying administrators about emergency")
		}
	}
}

// ClearEmergency 清除应急模式
func (ec *EmergencyController) ClearEmergency() {
	ec.mutex.Lock()
	defer ec.mutex.Unlock()

	if ec.emergencyMode {
		ec.emergencyMode = false
		ec.logger.Info("Emergency mode cleared")
	}
}

// LogRiskEvent 记录风险事件
func (ral *RiskAuditLogger) LogRiskEvent(event *RiskEvent) {
	ral.mutex.Lock()
	defer ral.mutex.Unlock()

	auditLog := &AuditLog{
		ID:        fmt.Sprintf("audit_%d", time.Now().UnixNano()),
		Timestamp: event.Timestamp,
		EventType: "risk_assessment",
		UserID:    event.UserID,
		SessionID: event.SessionID,
		Command:   event.Command,
		RiskScore: event.RiskScore,
		Action:    event.Action,
		Outcome:   event.Outcome,
		Metadata:  make(map[string]interface{}),
	}

	ral.auditLogs = append(ral.auditLogs, auditLog)

	ral.logger.WithFields(logrus.Fields{
		"audit_id":   auditLog.ID,
		"command":    event.Command,
		"risk_score": event.RiskScore,
		"action":     event.Action,
		"user_id":    event.UserID,
	}).Info("Risk event logged")
}

// UpdateRiskMetrics 更新风险指标
func (rtrm *RealTimeRiskMonitor) UpdateRiskMetrics(assessment *RiskAssessment, decision *RiskControlDecision) {
	rtrm.mutex.Lock()
	defer rtrm.mutex.Unlock()

	// 更新风险趋势
	hour := time.Now().Hour()
	if hour < len(rtrm.riskTrends.HourlyRiskScores) {
		rtrm.riskTrends.HourlyRiskScores[hour] = assessment.RiskScore
	}

	// 检查是否需要创建活跃风险
	if assessment.RiskScore >= rtrm.alertThresholds.RiskScoreThreshold {
		riskID := fmt.Sprintf("risk_%s_%d", assessment.Command, time.Now().Unix())
		activeRisk := &ActiveRisk{
			ID:              riskID,
			Type:            "command_risk",
			Severity:        assessment.RiskLevel,
			Description:     fmt.Sprintf("High risk command: %s", assessment.Command),
			FirstDetected:   time.Now(),
			LastSeen:        time.Now(),
			Frequency:       1,
			AffectedSystems: []string{"command_execution"},
			Status:          "active",
		}

		if existing, exists := rtrm.activeRisks[assessment.Command]; exists {
			existing.LastSeen = time.Now()
			existing.Frequency++
		} else {
			rtrm.activeRisks[assessment.Command] = activeRisk
		}
	}
}

// updateRiskTrends 更新风险趋势
func (rtrm *RealTimeRiskMonitor) updateRiskTrends() {
	rtrm.mutex.Lock()
	defer rtrm.mutex.Unlock()

	// 计算趋势方向
	if len(rtrm.riskTrends.HourlyRiskScores) >= 2 {
		recent := rtrm.riskTrends.HourlyRiskScores[len(rtrm.riskTrends.HourlyRiskScores)-1]
		previous := rtrm.riskTrends.HourlyRiskScores[len(rtrm.riskTrends.HourlyRiskScores)-2]

		if recent > previous+rtrm.alertThresholds.TrendThreshold {
			rtrm.riskTrends.TrendDirection = "increasing"
		} else if recent < previous-rtrm.alertThresholds.TrendThreshold {
			rtrm.riskTrends.TrendDirection = "decreasing"
		} else {
			rtrm.riskTrends.TrendDirection = "stable"
		}
	}

	rtrm.riskTrends.LastUpdated = time.Now()
}

// detectAnomalies 检测异常
func (rtrm *RealTimeRiskMonitor) detectAnomalies() {
	rtrm.mutex.RLock()
	defer rtrm.mutex.RUnlock()

	// 检查活跃风险的频率异常
	for _, risk := range rtrm.activeRisks {
		if risk.Frequency > rtrm.alertThresholds.FrequencyThreshold {
			rtrm.logger.WithFields(logrus.Fields{
				"risk_id":   risk.ID,
				"frequency": risk.Frequency,
				"threshold": rtrm.alertThresholds.FrequencyThreshold,
			}).Warn("Risk frequency anomaly detected")
		}
	}
}

// sendRiskAlert 发送风险告警
func (drc *DynamicRiskController) sendRiskAlert(assessment *RiskAssessment, decision *RiskControlDecision, userID int64) {
	if drc.alertManager == nil {
		return
	}

	alert := &Alert{
		ID:          fmt.Sprintf("alert_%d", time.Now().UnixNano()),
		Type:        "risk_alert",
		Severity:    assessment.RiskLevel,
		Title:       fmt.Sprintf("High Risk Command Detected: %s", assessment.Command),
		Description: fmt.Sprintf("User %d attempted to execute high risk command with score %.2f", userID, assessment.RiskScore),
		Timestamp:   time.Now(),
		Source:      "dynamic_risk_controller",
		Tags:        []string{"risk", "command", assessment.RiskLevel},
		Metadata: map[string]interface{}{
			"command":    assessment.Command,
			"risk_score": assessment.RiskScore,
			"action":     decision.Action,
			"user_id":    userID,
		},
		Status: "new",
	}

	drc.alertManager.SendAlert(alert)
}

// SendAlert 发送告警
func (ram *RiskAlertManager) SendAlert(alert *Alert) {
	ram.mutex.Lock()
	defer ram.mutex.Unlock()

	// 添加到历史记录
	ram.alertHistory = append(ram.alertHistory, alert)

	// 发送到各个通道
	for _, channel := range ram.alertChannels {
		if channel.IsEnabled() {
			if err := channel.SendAlert(alert); err != nil {
				ram.logger.WithError(err).WithField("channel", channel.GetChannelType()).Error("Failed to send alert")
			}
		}
	}

	ram.logger.WithFields(logrus.Fields{
		"alert_id": alert.ID,
		"type":     alert.Type,
		"severity": alert.Severity,
		"title":    alert.Title,
	}).Info("Alert sent")
}

// loadDefaultPolicies 加载默认策略
func (drc *DynamicRiskController) loadDefaultPolicies() {
	// 极高风险策略
	criticalPolicy := &RiskPolicy{
		ID:          "critical_risk_policy",
		Name:        "极高风险命令策略",
		Description: "对极高风险命令的控制策略",
		RiskLevel:   "critical",
		Action:      "block",
		Conditions: []*PolicyCondition{
			{
				Type:     "risk_score",
				Field:    "score",
				Operator: "gt",
				Value:    0.9,
				Weight:   1.0,
			},
		},
		Restrictions: &PolicyRestrictions{
			RequiredApprovers: []string{"admin"},
			RequiredBackup:    true,
		},
		Notifications: &PolicyNotifications{
			NotifyOnBlock: true,
			Recipients:    []string{"<EMAIL>"},
			Channels:      []string{"email", "slack"},
		},
		Enabled:   true,
		Priority:  100,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Metadata:  make(map[string]interface{}),
	}

	// 高风险策略
	highRiskPolicy := &RiskPolicy{
		ID:          "high_risk_policy",
		Name:        "高风险命令策略",
		Description: "对高风险命令的控制策略",
		RiskLevel:   "high",
		Action:      "require_approval",
		Conditions: []*PolicyCondition{
			{
				Type:     "risk_score",
				Field:    "score",
				Operator: "gt",
				Value:    0.7,
				Weight:   1.0,
			},
		},
		Restrictions: &PolicyRestrictions{
			MaxExecutionsPerHour: 5,
			RequiredApprovers:    []string{"supervisor"},
		},
		Notifications: &PolicyNotifications{
			NotifyOnApproval: true,
			Recipients:       []string{"<EMAIL>"},
			Channels:         []string{"email"},
		},
		Enabled:   true,
		Priority:  80,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Metadata:  make(map[string]interface{}),
	}

	// 生产环境策略
	productionPolicy := &RiskPolicy{
		ID:          "production_env_policy",
		Name:        "生产环境策略",
		Description: "生产环境下的额外控制策略",
		RiskLevel:   "",
		Action:      "sandbox",
		Conditions: []*PolicyCondition{
			{
				Type:     "environment",
				Field:    "env",
				Operator: "eq",
				Value:    "production",
				Weight:   1.0,
			},
			{
				Type:     "risk_score",
				Field:    "score",
				Operator: "gt",
				Value:    0.5,
				Weight:   0.8,
			},
		},
		Restrictions: &PolicyRestrictions{
			MandatorySandbox: true,
			RequiredBackup:   true,
		},
		Notifications: &PolicyNotifications{
			NotifyOnExecution: true,
			Recipients:        []string{"<EMAIL>"},
			Channels:          []string{"slack"},
		},
		Enabled:   true,
		Priority:  60,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Metadata:  make(map[string]interface{}),
	}

	// 添加到策略映射
	drc.riskPolicies[criticalPolicy.ID] = criticalPolicy
	drc.riskPolicies[highRiskPolicy.ID] = highRiskPolicy
	drc.riskPolicies[productionPolicy.ID] = productionPolicy

	drc.logger.Info("Default risk policies loaded")
}

// GetStatus 获取动态风险控制器状态
func (drc *DynamicRiskController) GetStatus() *DynamicRiskControllerStatus {
	drc.mutex.RLock()
	defer drc.mutex.RUnlock()

	status := &DynamicRiskControllerStatus{
		IsRunning:         drc.isRunning,
		EmergencyMode:     drc.emergencyController != nil && drc.emergencyController.IsEmergencyMode(),
		ActivePolicies:    len(drc.riskPolicies),
		CurrentThresholds: drc.riskThresholds,
		Config:            drc.config,
	}

	if drc.realTimeMonitor != nil {
		status.ActiveRisks = len(drc.realTimeMonitor.activeRisks)
		status.RiskTrends = drc.realTimeMonitor.riskTrends
	}

	if drc.adaptiveController != nil {
		status.PerformanceMetrics = drc.adaptiveController.performanceMetrics
	}

	return status
}

// DynamicRiskControllerStatus 动态风险控制器状态
type DynamicRiskControllerStatus struct {
	IsRunning          bool                `json:"is_running"`
	EmergencyMode      bool                `json:"emergency_mode"`
	ActivePolicies     int                 `json:"active_policies"`
	ActiveRisks        int                 `json:"active_risks"`
	CurrentThresholds  *DynamicThresholds  `json:"current_thresholds"`
	RiskTrends         *RiskTrends         `json:"risk_trends"`
	PerformanceMetrics *PerformanceMetrics `json:"performance_metrics"`
	Config             *DynamicRiskConfig  `json:"config"`
}
