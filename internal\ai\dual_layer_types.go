package ai

import (
	"context"
	"time"
)

// ===== 第一层：意图分类器相关类型 =====

// IntentClassifier 第一层意图分类器接口
type IntentClassifier interface {
	ClassifyIntent(ctx context.Context, message string, context *ConversationContext) (*ClassificationResult, error)
	GetSupportedIntents() []string
	UpdateContext(sessionID string, result *ClassificationResult) error
}

// ClassificationResult 意图分类结果
type ClassificationResult struct {
	Intent     string                 `json:"intent"`
	Confidence float64                `json:"confidence"`
	Entities   map[string]interface{} `json:"entities"`
	Context    map[string]interface{} `json:"context"`
	Reasoning  string                 `json:"reasoning"`
	Timestamp  time.Time              `json:"timestamp"`
}

// ClassifierConfig 分类器配置
type ClassifierConfig struct {
	ConfidenceThreshold float64 `json:"confidence_threshold"`
	MaxRetries          int     `json:"max_retries"`
	TimeoutSeconds      int     `json:"timeout_seconds"`
	EnableContextBoost  bool    `json:"enable_context_boost"`
	ContextBoostFactor  float64 `json:"context_boost_factor"`
}

// ===== 第二层：参数推断器相关类型 =====

// ParameterInferenceEngine 第二层参数推断器接口
type ParameterInferenceEngine interface {
	InferParameters(ctx context.Context, classification *ClassificationResult, message string, context *ConversationContext) (*InferenceResult, error)
	GetScenarioHandler(intent string) (ScenarioHandler, error)
	RegisterScenarioHandler(intent string, handler ScenarioHandler) error
}

// InferenceResult 参数推断结果
type InferenceResult struct {
	Parameters    map[string]interface{} `json:"parameters"`
	Commands      []CommandSequence      `json:"commands"`
	ExecutionPlan *ExecutionPlan         `json:"execution_plan"`
	Explanation   string                 `json:"explanation"`
	Confidence    float64                `json:"confidence"`
	Timestamp     time.Time              `json:"timestamp"`
}

// CommandSequence 命令序列
type CommandSequence struct {
	Step        int               `json:"step"`
	Command     string            `json:"command"`
	Description string            `json:"description"`
	Parameters  map[string]string `json:"parameters"`
	Timeout     int               `json:"timeout"`
	Required    bool              `json:"required"`
	OnError     string            `json:"on_error"` // continue, stop, retry
}

// ExecutionPlan 执行计划
type ExecutionPlan struct {
	Strategy      string            `json:"strategy"`       // sequential, parallel, conditional
	Timeout       int               `json:"timeout"`        // 总超时时间（秒）
	ErrorHandling string            `json:"error_handling"` // continue_on_error, stop_on_error, retry_on_error
	Prerequisites []string          `json:"prerequisites"`  // 前置条件
	Variables     map[string]string `json:"variables"`      // 执行变量
}

// ===== 场景处理器相关类型 =====

// ScenarioHandler 场景处理器接口
type ScenarioHandler interface {
	CanHandle(intent string, entities map[string]interface{}) bool
	GenerateCommands(ctx context.Context, classification *ClassificationResult, message string) ([]CommandSequence, error)
	GenerateExecutionPlan(commands []CommandSequence) *ExecutionPlan
	GetDescription() string
}

// ===== 统一调度器相关类型 =====

// UnifiedDispatcher 统一调度器接口
type UnifiedDispatcher interface {
	ProcessMessage(ctx context.Context, req *DualLayerRequest) (*DualLayerResponse, error)
	GetProcessingStatus(sessionID string) (*ProcessingStatus, error)
	CancelProcessing(sessionID string) error
}

// DualLayerRequest 双层处理请求
type DualLayerRequest struct {
	SessionID string                 `json:"session_id"`
	UserID    int64                  `json:"user_id"`
	Message   string                 `json:"message"`
	Context   *ConversationContext   `json:"context"`
	Options   map[string]interface{} `json:"options"`
}

// DualLayerResponse 双层处理响应
type DualLayerResponse struct {
	Content        string                 `json:"content"`
	Intent         string                 `json:"intent"`
	Confidence     float64                `json:"confidence"`
	Parameters     map[string]interface{} `json:"parameters"`
	Commands       []CommandSequence      `json:"commands"`
	ExecutionPlan  *ExecutionPlan         `json:"execution_plan"`
	Explanation    string                 `json:"explanation"`
	RequiresAction bool                   `json:"requires_action"`
	ActionType     string                 `json:"action_type"`
	ProcessingTime time.Duration          `json:"processing_time"`
	Timestamp      time.Time              `json:"timestamp"`
}

// ProcessingStatus 处理状态
type ProcessingStatus struct {
	SessionID     string    `json:"session_id"`
	Stage         string    `json:"stage"`    // classification, inference, execution
	Progress      float64   `json:"progress"` // 0.0 - 1.0
	CurrentStep   string    `json:"current_step"`
	EstimatedTime int       `json:"estimated_time"` // 预估剩余时间（秒）
	StartTime     time.Time `json:"start_time"`
	LastUpdate    time.Time `json:"last_update"`
}

// ===== 支持的意图类型常量 =====

const (
	// 主机状态诊断相关（新增）
	IntentHostStatusDiagnosis = "host_status_diagnosis"
	IntentSSHConnectionTest   = "ssh_connection_test"

	// 连接诊断相关
	IntentConnectionDiagnosis = "connection_diagnosis"
	IntentSSHDiagnosis        = "ssh_diagnosis"
	IntentNetworkDiagnosis    = "network_diagnosis"

	// 命令执行相关
	IntentCommandExecution = "command_execution"
	IntentScriptExecution  = "script_execution"

	// 系统监控相关
	IntentSystemMonitoring    = "system_monitoring"
	IntentPerformanceAnalysis = "performance_analysis"
	IntentResourceCheck       = "resource_check"

	// 服务管理相关
	IntentServiceManagement = "service_management"
	IntentProcessManagement = "process_management"

	// 日志分析相关
	IntentLogAnalysis = "log_analysis"
	IntentLogSearch   = "log_search"

	// 文件操作相关
	IntentFileOperations = "file_operations"
	IntentFileTransfer   = "file_transfer"

	// 安全检查相关
	IntentSecurityCheck   = "security_check"
	IntentPermissionCheck = "permission_check"

	// 主机管理相关
	IntentHostManagement    = "host_management"
	IntentHostConfiguration = "host_configuration"

	// 通用对话
	IntentGeneralChat = "general_chat"
	IntentHelp        = "help"
)

// ===== 实体类型常量 =====

const (
	EntityIPAddress   = "ip_address"
	EntityHostname    = "hostname"
	EntityPort        = "port"
	EntityCommand     = "command"
	EntityServiceName = "service_name"
	EntityFilePath    = "file_path"
	EntityLogLevel    = "log_level"
	EntityTimeRange   = "time_range"
	EntityUserName    = "username"
	EntityProcessName = "process_name"
)

// ===== 错误类型 =====

type DualLayerError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Stage   string `json:"stage"`
	Details string `json:"details"`
}

func (e *DualLayerError) Error() string {
	return e.Message
}

// 错误代码常量
const (
	ErrorCodeClassificationFailed = "CLASSIFICATION_FAILED"
	ErrorCodeInferenceFailed      = "INFERENCE_FAILED"
	ErrorCodeExecutionFailed      = "EXECUTION_FAILED"
	ErrorCodeInvalidInput         = "INVALID_INPUT"
	ErrorCodeTimeout              = "TIMEOUT"
	ErrorCodeServiceUnavailable   = "SERVICE_UNAVAILABLE"
)

// ===== 工具函数 =====

// NewClassificationResult 创建分类结果
func NewClassificationResult(intent string, confidence float64) *ClassificationResult {
	return &ClassificationResult{
		Intent:     intent,
		Confidence: confidence,
		Entities:   make(map[string]interface{}),
		Context:    make(map[string]interface{}),
		Timestamp:  time.Now(),
	}
}

// NewInferenceResult 创建推断结果
func NewInferenceResult() *InferenceResult {
	return &InferenceResult{
		Parameters: make(map[string]interface{}),
		Commands:   make([]CommandSequence, 0),
		Timestamp:  time.Now(),
	}
}

// NewDualLayerError 创建双层错误
func NewDualLayerError(code, message, stage string) *DualLayerError {
	return &DualLayerError{
		Code:    code,
		Message: message,
		Stage:   stage,
	}
}

// IsHighConfidence 判断是否为高置信度
func (cr *ClassificationResult) IsHighConfidence(threshold float64) bool {
	return cr.Confidence >= threshold
}

// HasEntity 检查是否包含指定实体
func (cr *ClassificationResult) HasEntity(entityType string) bool {
	_, exists := cr.Entities[entityType]
	return exists
}

// GetEntity 获取指定实体
func (cr *ClassificationResult) GetEntity(entityType string) (interface{}, bool) {
	value, exists := cr.Entities[entityType]
	return value, exists
}

// AddEntity 添加实体
func (cr *ClassificationResult) AddEntity(entityType string, value interface{}) {
	if cr.Entities == nil {
		cr.Entities = make(map[string]interface{})
	}
	cr.Entities[entityType] = value
}
