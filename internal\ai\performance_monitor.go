package ai

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	metrics         *PerformanceMetrics
	collectors      map[string]MetricCollector
	alertRules      []*AlertRule
	alertHandlers   []AlertHandler
	config          *MonitorConfig
	logger          *logrus.Logger
	mutex           sync.RWMutex
	isRunning       bool
	stop<PERSON>han        chan struct{}
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	// 系统指标
	CPUUsage        float64 `json:"cpu_usage"`
	MemoryUsage     float64 `json:"memory_usage"`
	GoroutineCount  int     `json:"goroutine_count"`
	HeapSize        int64   `json:"heap_size"`
	HeapInUse       int64   `json:"heap_in_use"`
	
	// DeepSeek API指标
	DeepSeekRequests     int64   `json:"deepseek_requests"`
	DeepSeekErrors       int64   `json:"deepseek_errors"`
	DeepSeekAvgLatency   float64 `json:"deepseek_avg_latency_ms"`
	DeepSeekSuccessRate  float64 `json:"deepseek_success_rate"`
	
	// Agent指标
	ActiveAgents         int     `json:"active_agents"`
	AgentExecutions      int64   `json:"agent_executions"`
	AgentErrors          int64   `json:"agent_errors"`
	AgentAvgLatency      float64 `json:"agent_avg_latency_ms"`
	AgentSuccessRate     float64 `json:"agent_success_rate"`
	
	// 缓存指标
	CacheHitRatio        float64 `json:"cache_hit_ratio"`
	CacheSize            int64   `json:"cache_size"`
	CacheEvictions       int64   `json:"cache_evictions"`
	
	// 业务指标
	TotalRequests        int64   `json:"total_requests"`
	SuccessfulRequests   int64   `json:"successful_requests"`
	FailedRequests       int64   `json:"failed_requests"`
	AvgResponseTime      float64 `json:"avg_response_time_ms"`
	
	// 时间戳
	Timestamp            time.Time `json:"timestamp"`
	CollectionInterval   time.Duration `json:"collection_interval"`
}

// MetricCollector 指标收集器接口
type MetricCollector interface {
	CollectMetrics(ctx context.Context) (map[string]interface{}, error)
	GetName() string
	GetInterval() time.Duration
}

// AlertRule 告警规则
type AlertRule struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Condition   string                 `json:"condition"`
	Threshold   float64                `json:"threshold"`
	Severity    AlertSeverity          `json:"severity"`
	Enabled     bool                   `json:"enabled"`
	Cooldown    time.Duration          `json:"cooldown"`
	LastTriggered *time.Time           `json:"last_triggered,omitempty"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// AlertSeverity 告警严重程度
type AlertSeverity string

const (
	SeverityInfo     AlertSeverity = "info"
	SeverityWarning  AlertSeverity = "warning"
	SeverityError    AlertSeverity = "error"
	SeverityCritical AlertSeverity = "critical"
)

// AlertEvent 告警事件
type AlertEvent struct {
	RuleID      string                 `json:"rule_id"`
	RuleName    string                 `json:"rule_name"`
	Severity    AlertSeverity          `json:"severity"`
	Message     string                 `json:"message"`
	Value       float64                `json:"value"`
	Threshold   float64                `json:"threshold"`
	Timestamp   time.Time              `json:"timestamp"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// AlertHandler 告警处理器接口
type AlertHandler interface {
	HandleAlert(ctx context.Context, event *AlertEvent) error
	GetName() string
}

// MonitorConfig 监控配置
type MonitorConfig struct {
	CollectionInterval   time.Duration `json:"collection_interval"`
	EnableSystemMetrics  bool          `json:"enable_system_metrics"`
	EnableBusinessMetrics bool         `json:"enable_business_metrics"`
	EnableAlerting       bool          `json:"enable_alerting"`
	MetricsRetention     time.Duration `json:"metrics_retention"`
	AlertCooldown        time.Duration `json:"alert_cooldown"`
}

// NewPerformanceMonitor 创建性能监控器
func NewPerformanceMonitor(config *MonitorConfig, logger *logrus.Logger) *PerformanceMonitor {
	if config == nil {
		config = &MonitorConfig{
			CollectionInterval:    30 * time.Second,
			EnableSystemMetrics:   true,
			EnableBusinessMetrics: true,
			EnableAlerting:        true,
			MetricsRetention:      24 * time.Hour,
			AlertCooldown:         5 * time.Minute,
		}
	}

	pm := &PerformanceMonitor{
		metrics:       &PerformanceMetrics{},
		collectors:    make(map[string]MetricCollector),
		alertRules:    make([]*AlertRule, 0),
		alertHandlers: make([]AlertHandler, 0),
		config:        config,
		logger:        logger,
		stopChan:      make(chan struct{}),
	}

	// 注册默认收集器
	pm.registerDefaultCollectors()

	// 注册默认告警规则
	pm.registerDefaultAlertRules()

	return pm
}

// Start 启动性能监控
func (pm *PerformanceMonitor) Start(ctx context.Context) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if pm.isRunning {
		return fmt.Errorf("performance monitor is already running")
	}

	pm.isRunning = true
	pm.logger.Info("Starting performance monitor")

	// 启动指标收集
	go pm.startMetricCollection(ctx)

	// 启动告警检查
	if pm.config.EnableAlerting {
		go pm.startAlertChecking(ctx)
	}

	return nil
}

// Stop 停止性能监控
func (pm *PerformanceMonitor) Stop() error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if !pm.isRunning {
		return fmt.Errorf("performance monitor is not running")
	}

	pm.logger.Info("Stopping performance monitor")
	close(pm.stopChan)
	pm.isRunning = false

	return nil
}

// GetMetrics 获取当前指标
func (pm *PerformanceMonitor) GetMetrics() *PerformanceMetrics {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 返回指标副本
	metrics := *pm.metrics
	return &metrics
}

// RegisterCollector 注册指标收集器
func (pm *PerformanceMonitor) RegisterCollector(collector MetricCollector) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.collectors[collector.GetName()] = collector
	pm.logger.WithField("collector", collector.GetName()).Info("Registered metric collector")
}

// RegisterAlertRule 注册告警规则
func (pm *PerformanceMonitor) RegisterAlertRule(rule *AlertRule) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.alertRules = append(pm.alertRules, rule)
	pm.logger.WithFields(logrus.Fields{
		"rule_id":   rule.ID,
		"rule_name": rule.Name,
		"severity":  rule.Severity,
	}).Info("Registered alert rule")
}

// RegisterAlertHandler 注册告警处理器
func (pm *PerformanceMonitor) RegisterAlertHandler(handler AlertHandler) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.alertHandlers = append(pm.alertHandlers, handler)
	pm.logger.WithField("handler", handler.GetName()).Info("Registered alert handler")
}

// 私有方法

func (pm *PerformanceMonitor) startMetricCollection(ctx context.Context) {
	ticker := time.NewTicker(pm.config.CollectionInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-pm.stopChan:
			return
		case <-ticker.C:
			pm.collectMetrics(ctx)
		}
	}
}

func (pm *PerformanceMonitor) collectMetrics(ctx context.Context) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// 收集系统指标
	if pm.config.EnableSystemMetrics {
		pm.collectSystemMetrics()
	}

	// 收集业务指标
	if pm.config.EnableBusinessMetrics {
		pm.collectBusinessMetrics(ctx)
	}

	// 更新时间戳
	pm.metrics.Timestamp = time.Now()
	pm.metrics.CollectionInterval = pm.config.CollectionInterval

	pm.logger.Debug("Metrics collected successfully")
}

func (pm *PerformanceMonitor) collectSystemMetrics() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	pm.metrics.GoroutineCount = runtime.NumGoroutine()
	pm.metrics.HeapSize = int64(m.HeapSys)
	pm.metrics.HeapInUse = int64(m.HeapInuse)
	pm.metrics.MemoryUsage = float64(m.HeapInuse) / float64(m.HeapSys) * 100
}

func (pm *PerformanceMonitor) collectBusinessMetrics(ctx context.Context) {
	// 从各个收集器收集指标
	for name, collector := range pm.collectors {
		metrics, err := collector.CollectMetrics(ctx)
		if err != nil {
			pm.logger.WithError(err).WithField("collector", name).Warn("Failed to collect metrics")
			continue
		}

		// 更新相应的指标
		pm.updateMetricsFromCollector(name, metrics)
	}
}

func (pm *PerformanceMonitor) updateMetricsFromCollector(collectorName string, metrics map[string]interface{}) {
	switch collectorName {
	case "deepseek":
		if requests, ok := metrics["requests"].(int64); ok {
			pm.metrics.DeepSeekRequests = requests
		}
		if errors, ok := metrics["errors"].(int64); ok {
			pm.metrics.DeepSeekErrors = errors
		}
		if latency, ok := metrics["avg_latency"].(float64); ok {
			pm.metrics.DeepSeekAvgLatency = latency
		}
		if pm.metrics.DeepSeekRequests > 0 {
			pm.metrics.DeepSeekSuccessRate = float64(pm.metrics.DeepSeekRequests-pm.metrics.DeepSeekErrors) / float64(pm.metrics.DeepSeekRequests) * 100
		}

	case "agent":
		if active, ok := metrics["active_agents"].(int); ok {
			pm.metrics.ActiveAgents = active
		}
		if executions, ok := metrics["executions"].(int64); ok {
			pm.metrics.AgentExecutions = executions
		}
		if errors, ok := metrics["errors"].(int64); ok {
			pm.metrics.AgentErrors = errors
		}
		if latency, ok := metrics["avg_latency"].(float64); ok {
			pm.metrics.AgentAvgLatency = latency
		}
		if pm.metrics.AgentExecutions > 0 {
			pm.metrics.AgentSuccessRate = float64(pm.metrics.AgentExecutions-pm.metrics.AgentErrors) / float64(pm.metrics.AgentExecutions) * 100
		}

	case "cache":
		if hitRatio, ok := metrics["hit_ratio"].(float64); ok {
			pm.metrics.CacheHitRatio = hitRatio
		}
		if size, ok := metrics["size"].(int64); ok {
			pm.metrics.CacheSize = size
		}
		if evictions, ok := metrics["evictions"].(int64); ok {
			pm.metrics.CacheEvictions = evictions
		}
	}
}

func (pm *PerformanceMonitor) startAlertChecking(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second) // 告警检查间隔
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-pm.stopChan:
			return
		case <-ticker.C:
			pm.checkAlerts(ctx)
		}
	}
}

func (pm *PerformanceMonitor) checkAlerts(ctx context.Context) {
	pm.mutex.RLock()
	metrics := *pm.metrics
	rules := make([]*AlertRule, len(pm.alertRules))
	copy(rules, pm.alertRules)
	pm.mutex.RUnlock()

	for _, rule := range rules {
		if !rule.Enabled {
			continue
		}

		// 检查冷却时间
		if rule.LastTriggered != nil && time.Since(*rule.LastTriggered) < rule.Cooldown {
			continue
		}

		// 评估告警条件
		if pm.evaluateAlertCondition(rule, &metrics) {
			pm.triggerAlert(ctx, rule, &metrics)
		}
	}
}

func (pm *PerformanceMonitor) evaluateAlertCondition(rule *AlertRule, metrics *PerformanceMetrics) bool {
	switch rule.Condition {
	case "cpu_usage_high":
		return metrics.CPUUsage > rule.Threshold
	case "memory_usage_high":
		return metrics.MemoryUsage > rule.Threshold
	case "deepseek_error_rate_high":
		return (100 - metrics.DeepSeekSuccessRate) > rule.Threshold
	case "deepseek_latency_high":
		return metrics.DeepSeekAvgLatency > rule.Threshold
	case "agent_error_rate_high":
		return (100 - metrics.AgentSuccessRate) > rule.Threshold
	case "cache_hit_ratio_low":
		return metrics.CacheHitRatio < rule.Threshold
	default:
		return false
	}
}

func (pm *PerformanceMonitor) triggerAlert(ctx context.Context, rule *AlertRule, metrics *PerformanceMetrics) {
	now := time.Now()
	rule.LastTriggered = &now

	// 创建告警事件
	event := &AlertEvent{
		RuleID:    rule.ID,
		RuleName:  rule.Name,
		Severity:  rule.Severity,
		Message:   pm.generateAlertMessage(rule, metrics),
		Threshold: rule.Threshold,
		Timestamp: now,
		Metadata:  rule.Metadata,
	}

	// 设置当前值
	switch rule.Condition {
	case "cpu_usage_high":
		event.Value = metrics.CPUUsage
	case "memory_usage_high":
		event.Value = metrics.MemoryUsage
	case "deepseek_error_rate_high":
		event.Value = 100 - metrics.DeepSeekSuccessRate
	case "deepseek_latency_high":
		event.Value = metrics.DeepSeekAvgLatency
	case "agent_error_rate_high":
		event.Value = 100 - metrics.AgentSuccessRate
	case "cache_hit_ratio_low":
		event.Value = metrics.CacheHitRatio
	}

	pm.logger.WithFields(logrus.Fields{
		"rule_id":   rule.ID,
		"rule_name": rule.Name,
		"severity":  rule.Severity,
		"value":     event.Value,
		"threshold": rule.Threshold,
	}).Warn("Alert triggered")

	// 发送给所有告警处理器
	for _, handler := range pm.alertHandlers {
		go func(h AlertHandler) {
			if err := h.HandleAlert(ctx, event); err != nil {
				pm.logger.WithError(err).WithField("handler", h.GetName()).Error("Failed to handle alert")
			}
		}(handler)
	}
}

func (pm *PerformanceMonitor) generateAlertMessage(rule *AlertRule, metrics *PerformanceMetrics) string {
	switch rule.Condition {
	case "cpu_usage_high":
		return fmt.Sprintf("CPU使用率过高: %.2f%% (阈值: %.2f%%)", metrics.CPUUsage, rule.Threshold)
	case "memory_usage_high":
		return fmt.Sprintf("内存使用率过高: %.2f%% (阈值: %.2f%%)", metrics.MemoryUsage, rule.Threshold)
	case "deepseek_error_rate_high":
		return fmt.Sprintf("DeepSeek错误率过高: %.2f%% (阈值: %.2f%%)", 100-metrics.DeepSeekSuccessRate, rule.Threshold)
	case "deepseek_latency_high":
		return fmt.Sprintf("DeepSeek延迟过高: %.2fms (阈值: %.2fms)", metrics.DeepSeekAvgLatency, rule.Threshold)
	case "agent_error_rate_high":
		return fmt.Sprintf("Agent错误率过高: %.2f%% (阈值: %.2f%%)", 100-metrics.AgentSuccessRate, rule.Threshold)
	case "cache_hit_ratio_low":
		return fmt.Sprintf("缓存命中率过低: %.2f%% (阈值: %.2f%%)", metrics.CacheHitRatio, rule.Threshold)
	default:
		return fmt.Sprintf("告警条件触发: %s", rule.Name)
	}
}

func (pm *PerformanceMonitor) registerDefaultCollectors() {
	// 这里可以注册默认的指标收集器
	// 实际实现中需要创建具体的收集器
}

func (pm *PerformanceMonitor) registerDefaultAlertRules() {
	// CPU使用率告警
	pm.alertRules = append(pm.alertRules, &AlertRule{
		ID:        "cpu_usage_high",
		Name:      "CPU使用率过高",
		Condition: "cpu_usage_high",
		Threshold: 80.0,
		Severity:  SeverityWarning,
		Enabled:   true,
		Cooldown:  5 * time.Minute,
	})

	// 内存使用率告警
	pm.alertRules = append(pm.alertRules, &AlertRule{
		ID:        "memory_usage_high",
		Name:      "内存使用率过高",
		Condition: "memory_usage_high",
		Threshold: 85.0,
		Severity:  SeverityWarning,
		Enabled:   true,
		Cooldown:  5 * time.Minute,
	})

	// DeepSeek错误率告警
	pm.alertRules = append(pm.alertRules, &AlertRule{
		ID:        "deepseek_error_rate_high",
		Name:      "DeepSeek错误率过高",
		Condition: "deepseek_error_rate_high",
		Threshold: 10.0,
		Severity:  SeverityError,
		Enabled:   true,
		Cooldown:  3 * time.Minute,
	})

	// 缓存命中率告警
	pm.alertRules = append(pm.alertRules, &AlertRule{
		ID:        "cache_hit_ratio_low",
		Name:      "缓存命中率过低",
		Condition: "cache_hit_ratio_low",
		Threshold: 70.0,
		Severity:  SeverityWarning,
		Enabled:   true,
		Cooldown:  10 * time.Minute,
	})
}
