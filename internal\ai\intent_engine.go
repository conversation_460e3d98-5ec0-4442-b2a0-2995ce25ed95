package ai

import (
	"fmt"
	"regexp"
	"sort"
	"strings"

	"github.com/sirupsen/logrus"
)

// IntentEngine 意图识别引擎
type IntentEngine struct {
	patterns   map[string][]*IntentPattern
	entities   map[string]*EntityExtractor
	contextMgr *ContextManager
	logger     *logrus.Logger
	config     *IntentConfig
}

// IntentConfig 意图配置
type IntentConfig struct {
	ConfidenceThreshold float64 `json:"confidence_threshold"`
	MaxCandidates       int     `json:"max_candidates"`
	EnableContextBoost  bool    `json:"enable_context_boost"`
	ContextBoostFactor  float64 `json:"context_boost_factor"`
}

// IntentPattern 意图模式
type IntentPattern struct {
	ID         string                 `json:"id"`
	Intent     string                 `json:"intent"`
	Patterns   []string               `json:"patterns"`
	Keywords   []string               `json:"keywords"`
	Entities   []string               `json:"entities"`
	Context    []ContextType          `json:"context"`
	Priority   int                    `json:"priority"`
	Confidence float64                `json:"confidence"`
	Examples   []string               `json:"examples"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// EntityExtractor 实体提取器
type EntityExtractor struct {
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Patterns    []string               `json:"patterns"`
	Validators  []string               `json:"validators"`
	Normalizers []string               `json:"normalizers"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// IntentResult 意图识别结果
type IntentResult struct {
	Intent      string                 `json:"intent"`
	Confidence  float64                `json:"confidence"`
	Entities    map[string]interface{} `json:"entities"`
	Context     *ConversationContext   `json:"context"`
	Candidates  []*IntentCandidate     `json:"candidates"`
	Reasoning   string                 `json:"reasoning"`
	Suggestions []string               `json:"suggestions"`
}

// IntentCandidate 意图候选
type IntentCandidate struct {
	Intent     string  `json:"intent"`
	Confidence float64 `json:"confidence"`
	Score      float64 `json:"score"`
	Reason     string  `json:"reason"`
}

// NewIntentEngine 创建意图识别引擎
func NewIntentEngine(contextMgr *ContextManager, logger *logrus.Logger) *IntentEngine {
	config := &IntentConfig{
		ConfidenceThreshold: 0.7,
		MaxCandidates:       3,
		EnableContextBoost:  true,
		ContextBoostFactor:  0.2,
	}

	engine := &IntentEngine{
		patterns:   make(map[string][]*IntentPattern),
		entities:   make(map[string]*EntityExtractor),
		contextMgr: contextMgr,
		logger:     logger,
		config:     config,
	}

	// 初始化内置模式和实体
	engine.initializeBuiltinPatterns()
	engine.initializeBuiltinEntities()

	return engine
}

// initializeBuiltinPatterns 初始化内置意图模式
func (ie *IntentEngine) initializeBuiltinPatterns() {
	patterns := []*IntentPattern{
		// 主机管理意图
		{
			ID:         "host_connect",
			Intent:     "host_connect",
			Patterns:   []string{`连接\s*(?:到\s*)?(?:主机\s*)?(.+)`, `登录\s*(?:到\s*)?(.+)`, `ssh\s+(.+)`},
			Keywords:   []string{"连接", "登录", "ssh", "主机"},
			Entities:   []string{"host_ip", "hostname"},
			Context:    []ContextType{ContextTypeHost},
			Priority:   10,
			Confidence: 0.9,
		},
		{
			ID:         "host_add",
			Intent:     "host_add",
			Patterns:   []string{`添加\s*主机\s+(.+)\s+(.+)\s+(.+)`, `新增\s*主机\s+(.+)\s+(.+)\s+(.+)`},
			Keywords:   []string{"添加", "新增", "创建", "主机"},
			Entities:   []string{"host_ip", "username", "password"},
			Context:    []ContextType{ContextTypeHost},
			Priority:   10,
			Confidence: 0.95,
		},
		{
			ID:         "host_list",
			Intent:     "host_list",
			Patterns:   []string{`(?:列出|显示|查看|获取)\s*(?:所有\s*)?主机(?:列表)?`, `主机\s*(?:列表|清单)`},
			Keywords:   []string{"列出", "显示", "查看", "主机", "列表"},
			Entities:   []string{},
			Context:    []ContextType{ContextTypeHost},
			Priority:   8,
			Confidence: 0.9,
		},

		// 命令执行意图
		{
			ID:     "command_execute",
			Intent: "command_execute",
			Patterns: []string{
				`(?:在\s*(.+?)\s*(?:上|中))?\s*执行\s*(?:命令\s*)?(.+)`,
				`(?:在\s*(.+?)\s*(?:上|中))?\s*运行\s*(?:命令\s*)?(.+)`,
				`连接\s*(.+?)\s*(?:并|然后)\s*执行\s*(.+)`,
			},
			Keywords:   []string{"执行", "运行", "命令"},
			Entities:   []string{"host_ip", "command"},
			Context:    []ContextType{ContextTypeHost, ContextTypeCommand},
			Priority:   10,
			Confidence: 0.9,
		},

		// 监控查询意图
		{
			ID:     "monitoring_status",
			Intent: "monitoring_status",
			Patterns: []string{
				`(?:查看|显示|获取)\s*(?:(.+?)\s*(?:的|上))?\s*(?:系统\s*)?(?:状态|监控|性能)`,
				`(.+?)\s*(?:的\s*)?(?:CPU|内存|磁盘|网络)\s*(?:使用率|状态)`,
			},
			Keywords:   []string{"状态", "监控", "性能", "CPU", "内存", "磁盘"},
			Entities:   []string{"host_ip", "metric_type"},
			Context:    []ContextType{ContextTypeMonitoring},
			Priority:   8,
			Confidence: 0.85,
		},

		// 故障排查意图
		{
			ID:     "troubleshoot_start",
			Intent: "troubleshoot_start",
			Patterns: []string{
				`(.+?)\s*(?:有问题|出现问题|故障|异常|不正常)`,
				`排查\s*(.+?)\s*(?:的\s*)?(?:问题|故障|异常)`,
				`诊断\s*(.+?)`,
			},
			Keywords:   []string{"问题", "故障", "异常", "排查", "诊断"},
			Entities:   []string{"host_ip", "service_name", "issue_type"},
			Context:    []ContextType{ContextTypeTroubleshoot},
			Priority:   9,
			Confidence: 0.8,
		},

		// 工作流意图
		{
			ID:     "workflow_create",
			Intent: "workflow_create",
			Patterns: []string{
				`创建\s*(?:一个\s*)?(?:工作流|流程|任务)\s*(?:来\s*)?(.+)`,
				`自动化\s*(.+)`,
				`批量\s*(.+)`,
			},
			Keywords:   []string{"创建", "工作流", "自动化", "批量"},
			Entities:   []string{"workflow_name", "workflow_description"},
			Context:    []ContextType{ContextTypeWorkflow},
			Priority:   7,
			Confidence: 0.8,
		},

		// 帮助和查询意图
		{
			ID:         "help_general",
			Intent:     "help_general",
			Patterns:   []string{`帮助`, `help`, `怎么.*`, `如何.*`, `能做什么`},
			Keywords:   []string{"帮助", "help", "怎么", "如何"},
			Entities:   []string{},
			Context:    []ContextType{},
			Priority:   5,
			Confidence: 0.9,
		},

		// 上下文查询意图
		{
			ID:     "context_query",
			Intent: "context_query",
			Patterns: []string{
				`(?:当前\s*)?(?:状态|情况|上下文)(?:是什么|如何)?`,
				`我在(?:哪里|做什么)`,
				`现在(?:在哪|在做什么)`,
			},
			Keywords:   []string{"当前", "状态", "情况", "上下文"},
			Entities:   []string{},
			Context:    []ContextType{},
			Priority:   6,
			Confidence: 0.85,
		},
	}

	// 按意图分组存储模式
	for _, pattern := range patterns {
		ie.patterns[pattern.Intent] = append(ie.patterns[pattern.Intent], pattern)
	}
}

// initializeBuiltinEntities 初始化内置实体提取器
func (ie *IntentEngine) initializeBuiltinEntities() {
	entities := []*EntityExtractor{
		{
			Name:       "host_ip",
			Type:       "ip_address",
			Patterns:   []string{`\b(?:\d{1,3}\.){3}\d{1,3}\b`},
			Validators: []string{"validate_ip"},
		},
		{
			Name:     "hostname",
			Type:     "hostname",
			Patterns: []string{`\b[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\b`},
		},
		{
			Name:     "username",
			Type:     "username",
			Patterns: []string{`\b[a-zA-Z][a-zA-Z0-9_-]{2,31}\b`},
		},
		{
			Name:     "command",
			Type:     "shell_command",
			Patterns: []string{`[a-zA-Z][a-zA-Z0-9_-]*(?:\s+[^\s]+)*`},
		},
		{
			Name:     "service_name",
			Type:     "service",
			Patterns: []string{`\b(?:nginx|apache|mysql|redis|docker|ssh|ftp|dns|dhcp|ntp)\b`},
		},
		{
			Name:     "metric_type",
			Type:     "metric",
			Patterns: []string{`\b(?:cpu|memory|disk|network|load|temperature)\b`},
		},
		{
			Name:     "file_path",
			Type:     "path",
			Patterns: []string{`(?:/[^/\s]+)+/?`, `[a-zA-Z]:\\(?:[^\\/:*?"<>|\s]+\\)*[^\\/:*?"<>|\s]*`},
		},
	}

	for _, entity := range entities {
		ie.entities[entity.Name] = entity
	}
}

// RecognizeIntent 识别意图
func (ie *IntentEngine) RecognizeIntent(sessionID, message string) *IntentResult {
	// 获取上下文
	context := ie.contextMgr.GetOrCreateContext(sessionID, 1) // 暂时使用固定用户ID

	// 预处理消息
	normalizedMessage := ie.normalizeMessage(message)

	// 提取实体
	entities := ie.extractEntities(normalizedMessage)

	// 计算所有意图的匹配分数
	candidates := ie.calculateIntentScores(normalizedMessage, entities, context)

	// 排序候选意图
	sort.Slice(candidates, func(i, j int) bool {
		return candidates[i].Score > candidates[j].Score
	})

	// 限制候选数量
	if len(candidates) > ie.config.MaxCandidates {
		candidates = candidates[:ie.config.MaxCandidates]
	}

	// 确定最终意图
	var finalIntent string
	var finalConfidence float64
	var reasoning string

	if len(candidates) > 0 && candidates[0].Score >= ie.config.ConfidenceThreshold {
		finalIntent = candidates[0].Intent
		finalConfidence = candidates[0].Confidence
		reasoning = candidates[0].Reason
	} else {
		finalIntent = "unknown"
		finalConfidence = 0.0
		reasoning = "无法识别明确的意图"
	}

	result := &IntentResult{
		Intent:      finalIntent,
		Confidence:  finalConfidence,
		Entities:    entities,
		Context:     context,
		Candidates:  candidates,
		Reasoning:   reasoning,
		Suggestions: ie.generateSuggestions(normalizedMessage, candidates),
	}

	// 记录到上下文
	ie.contextMgr.AddMessage(sessionID, "user", message, finalIntent, entities)

	ie.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"message":    message,
		"intent":     finalIntent,
		"confidence": finalConfidence,
		"entities":   len(entities),
	}).Info("Intent recognition completed")

	return result
}

// normalizeMessage 标准化消息
func (ie *IntentEngine) normalizeMessage(message string) string {
	// 转换为小写
	normalized := strings.ToLower(message)

	// 移除多余的空格
	normalized = regexp.MustCompile(`\s+`).ReplaceAllString(normalized, " ")

	// 移除标点符号（保留必要的）
	normalized = regexp.MustCompile(`[^\w\s\.\-_/:]`).ReplaceAllString(normalized, " ")

	return strings.TrimSpace(normalized)
}

// extractEntities 提取实体
func (ie *IntentEngine) extractEntities(message string) map[string]interface{} {
	entities := make(map[string]interface{})

	for name, extractor := range ie.entities {
		for _, pattern := range extractor.Patterns {
			re := regexp.MustCompile(pattern)
			matches := re.FindAllString(message, -1)
			if len(matches) > 0 {
				if len(matches) == 1 {
					entities[name] = matches[0]
				} else {
					entities[name] = matches
				}
			}
		}
	}

	return entities
}

// calculateIntentScores 计算意图分数
func (ie *IntentEngine) calculateIntentScores(message string, entities map[string]interface{}, context *ConversationContext) []*IntentCandidate {
	candidates := make([]*IntentCandidate, 0)

	for intent, patterns := range ie.patterns {
		for _, pattern := range patterns {
			score := ie.calculatePatternScore(message, pattern, entities, context)
			if score > 0 {
				candidate := &IntentCandidate{
					Intent:     intent,
					Confidence: pattern.Confidence,
					Score:      score,
					Reason:     fmt.Sprintf("匹配模式: %s", pattern.ID),
				}
				candidates = append(candidates, candidate)
			}
		}
	}

	return candidates
}

// calculatePatternScore 计算模式分数
func (ie *IntentEngine) calculatePatternScore(message string, pattern *IntentPattern, entities map[string]interface{}, context *ConversationContext) float64 {
	score := 0.0

	// 模式匹配分数
	for _, p := range pattern.Patterns {
		if matched, _ := regexp.MatchString(p, message); matched {
			score += 0.4
			break
		}
	}

	// 关键词匹配分数
	keywordMatches := 0
	for _, keyword := range pattern.Keywords {
		if strings.Contains(message, keyword) {
			keywordMatches++
		}
	}
	if len(pattern.Keywords) > 0 {
		score += 0.3 * float64(keywordMatches) / float64(len(pattern.Keywords))
	}

	// 实体匹配分数
	entityMatches := 0
	for _, entityName := range pattern.Entities {
		if _, exists := entities[entityName]; exists {
			entityMatches++
		}
	}
	if len(pattern.Entities) > 0 {
		score += 0.2 * float64(entityMatches) / float64(len(pattern.Entities))
	}

	// 上下文增强
	if ie.config.EnableContextBoost && context != nil {
		for _, contextType := range pattern.Context {
			if context.Type == contextType {
				score += ie.config.ContextBoostFactor
				break
			}
		}
	}

	// 优先级调整
	score *= float64(pattern.Priority) / 10.0

	return score * pattern.Confidence
}

// generateSuggestions 生成建议
func (ie *IntentEngine) generateSuggestions(message string, candidates []*IntentCandidate) []string {
	suggestions := make([]string, 0)

	if len(candidates) == 0 {
		suggestions = append(suggestions, "您可以尝试：")
		suggestions = append(suggestions, "• 连接主机：连接*************")
		suggestions = append(suggestions, "• 执行命令：在*************上执行ls -la")
		suggestions = append(suggestions, "• 查看状态：显示主机状态")
		suggestions = append(suggestions, "• 获取帮助：帮助")
	} else if candidates[0].Score < 0.8 {
		suggestions = append(suggestions, "我理解您可能想要：")
		for i, candidate := range candidates {
			if i >= 2 {
				break
			}
			suggestions = append(suggestions, fmt.Sprintf("• %s (置信度: %.2f)", ie.getIntentDescription(candidate.Intent), candidate.Confidence))
		}
	}

	return suggestions
}

// getIntentDescription 获取意图描述
func (ie *IntentEngine) getIntentDescription(intent string) string {
	descriptions := map[string]string{
		"host_connect":       "连接到主机",
		"host_add":           "添加新主机",
		"host_list":          "列出主机",
		"command_execute":    "执行命令",
		"monitoring_status":  "查看监控状态",
		"troubleshoot_start": "开始故障排查",
		"workflow_create":    "创建工作流",
		"help_general":       "获取帮助",
		"context_query":      "查询当前状态",
	}

	if desc, exists := descriptions[intent]; exists {
		return desc
	}
	return intent
}
