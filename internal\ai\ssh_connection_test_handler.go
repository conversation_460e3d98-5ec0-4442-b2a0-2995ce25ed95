package ai

import (
	"context"
	"fmt"
	"strings"

	"aiops-platform/internal/workflow"

	"github.com/sirupsen/logrus"
)

// sshConnectionTestHandler SSH连接测试处理器
type sshConnectionTestHandler struct {
	deepseekService interface{} // 简化为interface{}
	hostService     workflow.HostServiceInterface
	logger          *logrus.Logger
}

// NewSSHConnectionTestHandler 创建SSH连接测试处理器
func NewSSHConnectionTestHandler(
	deepseekService interface{}, // 简化为interface{}
	hostService workflow.HostServiceInterface,
	logger *logrus.Logger,
) ScenarioHandler {
	return &sshConnectionTestHandler{
		deepseekService: deepseekService,
		hostService:     hostService,
		logger:          logger,
	}
}

// CanHandle 检查是否能处理此场景
func (scth *sshConnectionTestHandler) CanHandle(intent string, entities map[string]interface{}) bool {
	if intent != IntentSSHConnectionTest {
		return false
	}

	// 必须有IP地址或主机名
	_, hasIP := entities[EntityIPAddress]
	_, hasHostname := entities[EntityHostname]

	return hasIP || hasHostname
}

// GenerateCommands 生成SSH连接测试命令
func (scth *sshConnectionTestHandler) GenerateCommands(ctx context.Context, classification *ClassificationResult, message string) ([]CommandSequence, error) {
	targetHost := scth.extractTargetHost(classification.Entities)
	if targetHost == "" {
		return nil, fmt.Errorf("无法提取目标主机信息")
	}

	scth.logger.WithFields(logrus.Fields{
		"target_host": targetHost,
		"intent":      classification.Intent,
	}).Info("生成SSH连接测试命令")

	// 生成SSH连接测试命令序列
	return scth.generateSSHConnectionTestCommands(targetHost), nil
}

// GenerateExecutionPlan 生成执行计划
func (scth *sshConnectionTestHandler) GenerateExecutionPlan(commands []CommandSequence) *ExecutionPlan {
	return &ExecutionPlan{
		Strategy: "sequential",
		Timeout:  60, // 60秒总超时
		// 移除不存在的字段
	}
}

// GetDescription 获取处理器描述
func (scth *sshConnectionTestHandler) GetDescription() string {
	return "SSH connection test handler for diagnosing SSH connectivity issues"
}

// extractTargetHost 提取目标主机
func (scth *sshConnectionTestHandler) extractTargetHost(entities map[string]interface{}) string {
	if ip, exists := entities[EntityIPAddress]; exists {
		if ipStr, ok := ip.(string); ok {
			return ipStr
		}
	}

	if hostname, exists := entities[EntityHostname]; exists {
		if hostnameStr, ok := hostname.(string); ok {
			return hostnameStr
		}
	}

	return ""
}

// generateSSHConnectionTestCommands 生成SSH连接测试命令
func (scth *sshConnectionTestHandler) generateSSHConnectionTestCommands(targetHost string) []CommandSequence {
	return []CommandSequence{
		{
			Step:        1,
			Command:     fmt.Sprintf("nc -zv %s 22", targetHost),
			Description: "检查SSH端口(22)连通性",
			Parameters:  map[string]string{"host": targetHost, "port": "22"},
			Timeout:     10,
			Required:    true,
			OnError:     "continue",
		},
		{
			Step:        2,
			Command:     fmt.Sprintf("ssh -o ConnectTimeout=5 -o BatchMode=yes -o PasswordAuthentication=no %s 'echo ssh_key_test'", targetHost),
			Description: "测试SSH密钥认证",
			Parameters:  map[string]string{"host": targetHost, "auth_method": "key"},
			Timeout:     10,
			Required:    false,
			OnError:     "continue",
		},
		{
			Step:        3,
			Command:     fmt.Sprintf("ssh -o ConnectTimeout=5 -o PreferredAuthentications=password %s 'echo password_test'", targetHost),
			Description: "测试SSH密码认证",
			Parameters:  map[string]string{"host": targetHost, "auth_method": "password"},
			Timeout:     15,
			Required:    false,
			OnError:     "continue",
		},
		{
			Step:        4,
			Command:     fmt.Sprintf("ssh -vvv -o ConnectTimeout=5 %s 'exit' 2>&1 | head -20", targetHost),
			Description: "获取详细SSH连接日志",
			Parameters:  map[string]string{"host": targetHost, "verbose": "true"},
			Timeout:     15,
			Required:    false,
			OnError:     "continue",
		},
		{
			Step:        5,
			Command:     fmt.Sprintf("telnet %s 22", targetHost),
			Description: "使用telnet测试SSH端口",
			Parameters:  map[string]string{"host": targetHost, "port": "22", "protocol": "telnet"},
			Timeout:     8,
			Required:    false,
			OnError:     "continue",
		},
	}
}

// ExecuteWithHostService 使用主机服务执行SSH连接测试
func (scth *sshConnectionTestHandler) ExecuteWithHostService(ctx context.Context, targetHost string) (*SSHTestResult, error) {
	// 首先尝试通过IP地址查找主机
	hostID, err := scth.findHostByIP(targetHost)
	if err != nil {
		scth.logger.WithError(err).Warn("无法找到主机记录，将执行基础SSH测试")
		return scth.executeBasicSSHTest(ctx, targetHost)
	}

	// 使用主机服务进行SSH连接测试（workflow接口只返回error）
	err = scth.hostService.TestConnection(hostID)

	// 构建SSH测试结果
	success := err == nil
	errorMessage := ""
	details := []string{}
	suggestions := []string{}

	if success {
		details = append(details, "SSH连接成功")
		details = append(details, "主机SSH服务正常运行")
	} else {
		errorMessage = err.Error()
		details = append(details, fmt.Sprintf("SSH连接失败: %s", err.Error()))

		// 根据错误信息提供具体建议
		suggestions = scth.generateSSHErrorSuggestions(err.Error())
	}

	return &SSHTestResult{
		HostIP:       targetHost,
		Success:      success,
		ErrorMessage: errorMessage,
		Details:      details,
		Suggestions:  suggestions,
		Timestamp:    "now",
	}, nil
}

// findHostByIP 通过IP地址查找主机ID
func (scth *sshConnectionTestHandler) findHostByIP(ipAddress string) (int64, error) {
	// 这里需要实现通过IP地址查找主机ID的逻辑
	// 暂时返回错误，表示未找到
	return 0, fmt.Errorf("host not found for IP: %s", ipAddress)
}

// executeBasicSSHTest 执行基础SSH测试
func (scth *sshConnectionTestHandler) executeBasicSSHTest(ctx context.Context, targetHost string) (*SSHTestResult, error) {
	return &SSHTestResult{
		HostIP:       targetHost,
		Success:      false,
		ErrorMessage: "主机未在系统中注册",
		Details:      []string{"无法找到主机记录", "建议先添加主机到系统中"},
		Suggestions:  []string{"使用'添加主机'功能注册此主机", "确认IP地址是否正确"},
		Timestamp:    "now",
	}, nil
}

// generateSSHErrorSuggestions 根据错误信息生成建议
func (scth *sshConnectionTestHandler) generateSSHErrorSuggestions(errorMessage string) []string {
	suggestions := []string{}
	errorLower := strings.ToLower(errorMessage)

	if strings.Contains(errorLower, "connection refused") {
		suggestions = append(suggestions, "SSH服务可能未启动，请检查sshd服务状态")
		suggestions = append(suggestions, "检查防火墙是否阻止了22端口")
	}

	if strings.Contains(errorLower, "timeout") {
		suggestions = append(suggestions, "网络连接超时，检查网络连通性")
		suggestions = append(suggestions, "确认目标主机IP地址是否正确")
	}

	if strings.Contains(errorLower, "permission denied") {
		suggestions = append(suggestions, "认证失败，检查用户名和密码")
		suggestions = append(suggestions, "确认SSH密钥配置是否正确")
	}

	if strings.Contains(errorLower, "host key verification failed") {
		suggestions = append(suggestions, "主机密钥验证失败，可能是主机密钥已更改")
		suggestions = append(suggestions, "清理known_hosts文件中的旧密钥")
	}

	if len(suggestions) == 0 {
		suggestions = append(suggestions, "检查SSH服务是否正常运行")
		suggestions = append(suggestions, "验证网络连接和防火墙设置")
		suggestions = append(suggestions, "确认认证信息是否正确")
	}

	return suggestions
}

// SSHTestResult SSH测试结果
type SSHTestResult struct {
	HostIP       string   `json:"host_ip"`
	Success      bool     `json:"success"`
	ErrorMessage string   `json:"error_message,omitempty"`
	Details      []string `json:"details"`
	Suggestions  []string `json:"suggestions"`
	Timestamp    string   `json:"timestamp"`
}
