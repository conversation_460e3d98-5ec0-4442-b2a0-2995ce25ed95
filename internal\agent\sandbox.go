package agent

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// SandboxManager 沙箱管理器
type SandboxManager struct {
	config    *SandboxConfig
	logger    *logrus.Logger
	sandboxes map[string]*Sandbox
	mutex     sync.RWMutex
	basePath  string
	isEnabled bool
}

// SandboxConfig 沙箱配置
type SandboxConfig struct {
	Enabled          bool     `json:"enabled"`
	BasePath         string   `json:"base_path"`
	MaxSandboxes     int      `json:"max_sandboxes"`
	SandboxTimeout   int      `json:"sandbox_timeout"`  // 秒
	CleanupInterval  int      `json:"cleanup_interval"` // 秒
	AllowedMounts    []string `json:"allowed_mounts"`
	DeniedPaths      []string `json:"denied_paths"`
	MaxDiskUsageMB   int64    `json:"max_disk_usage_mb"`
	MaxMemoryMB      int64    `json:"max_memory_mb"`
	MaxProcesses     int      `json:"max_processes"`
	NetworkIsolation bool     `json:"network_isolation"`
	ReadOnlyMode     bool     `json:"read_only_mode"`
}

// Sandbox 沙箱实例
type Sandbox struct {
	ID          string                 `json:"id"`
	Path        string                 `json:"path"`
	CreatedAt   time.Time              `json:"created_at"`
	LastUsed    time.Time              `json:"last_used"`
	Status      string                 `json:"status"` // active, idle, cleanup
	ProcessID   int                    `json:"process_id"`
	UserID      int64                  `json:"user_id"`
	SessionID   string                 `json:"session_id"`
	Environment map[string]string      `json:"environment"`
	Mounts      []SandboxMount         `json:"mounts"`
	Resources   *SandboxResourceUsage  `json:"resources"`
	Metadata    map[string]interface{} `json:"metadata"`
	mutex       sync.RWMutex
}

// SandboxMount 沙箱挂载点
type SandboxMount struct {
	Source      string `json:"source"`
	Target      string `json:"target"`
	Type        string `json:"type"`    // bind, tmpfs, proc
	Options     string `json:"options"` // ro, rw, noexec
	Description string `json:"description"`
}

// SandboxResourceUsage 沙箱资源使用情况
type SandboxResourceUsage struct {
	MemoryMB     float64 `json:"memory_mb"`
	DiskUsageMB  float64 `json:"disk_usage_mb"`
	ProcessCount int     `json:"process_count"`
	CPUPercent   float64 `json:"cpu_percent"`
	NetworkInMB  float64 `json:"network_in_mb"`
	NetworkOutMB float64 `json:"network_out_mb"`
}

// NewSandboxManager 创建沙箱管理器
func NewSandboxManager(config *SandboxConfig, logger *logrus.Logger) *SandboxManager {
	if config == nil {
		config = DefaultSandboxConfig()
	}

	return &SandboxManager{
		config:    config,
		logger:    logger,
		sandboxes: make(map[string]*Sandbox),
		basePath:  config.BasePath,
		isEnabled: config.Enabled,
	}
}

// DefaultSandboxConfig 默认沙箱配置
func DefaultSandboxConfig() *SandboxConfig {
	return &SandboxConfig{
		Enabled:          true,
		BasePath:         "/tmp/aiops-sandbox",
		MaxSandboxes:     10,
		SandboxTimeout:   3600, // 1小时
		CleanupInterval:  300,  // 5分钟
		AllowedMounts:    []string{"/tmp", "/var/log"},
		DeniedPaths:      []string{"/etc", "/boot", "/sys", "/proc", "/dev"},
		MaxDiskUsageMB:   100,
		MaxMemoryMB:      256,
		MaxProcesses:     10,
		NetworkIsolation: true,
		ReadOnlyMode:     false,
	}
}

// Start 启动沙箱管理器
func (sm *SandboxManager) Start(ctx context.Context) error {
	if !sm.isEnabled {
		sm.logger.Info("Sandbox is disabled, skipping start")
		return nil
	}

	sm.logger.Info("Starting sandbox manager")

	// 创建基础目录
	if err := os.MkdirAll(sm.basePath, 0755); err != nil {
		return fmt.Errorf("failed to create sandbox base path: %w", err)
	}

	// 启动清理协程
	go sm.cleanupRoutine(ctx)

	sm.logger.Info("Sandbox manager started successfully")
	return nil
}

// Stop 停止沙箱管理器
func (sm *SandboxManager) Stop(ctx context.Context) error {
	if !sm.isEnabled {
		return nil
	}

	sm.logger.Info("Stopping sandbox manager")

	// 清理所有沙箱
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	for id, sandbox := range sm.sandboxes {
		if err := sm.destroySandboxInternal(sandbox); err != nil {
			sm.logger.WithError(err).WithField("sandbox_id", id).Error("Failed to destroy sandbox")
		}
	}

	sm.sandboxes = make(map[string]*Sandbox)
	sm.logger.Info("Sandbox manager stopped")
	return nil
}

// CreateSandbox 创建沙箱
func (sm *SandboxManager) CreateSandbox(ctx context.Context, userID int64, sessionID string) (*Sandbox, error) {
	if !sm.isEnabled {
		return nil, fmt.Errorf("sandbox is disabled")
	}

	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	// 检查沙箱数量限制
	if len(sm.sandboxes) >= sm.config.MaxSandboxes {
		return nil, fmt.Errorf("maximum number of sandboxes reached: %d", sm.config.MaxSandboxes)
	}

	// 生成沙箱ID和路径
	sandboxID := generateSandboxID()
	sandboxPath := filepath.Join(sm.basePath, sandboxID)

	// 创建沙箱目录
	if err := os.MkdirAll(sandboxPath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create sandbox directory: %w", err)
	}

	// 创建沙箱实例
	sandbox := &Sandbox{
		ID:        sandboxID,
		Path:      sandboxPath,
		CreatedAt: time.Now(),
		LastUsed:  time.Now(),
		Status:    "active",
		UserID:    userID,
		SessionID: sessionID,
		Environment: map[string]string{
			"HOME":   sandboxPath,
			"TMPDIR": filepath.Join(sandboxPath, "tmp"),
			"PATH":   "/usr/local/bin:/usr/bin:/bin",
			"USER":   "sandbox",
			"SHELL":  "/bin/bash",
		},
		Mounts:    []SandboxMount{},
		Resources: &SandboxResourceUsage{},
		Metadata:  make(map[string]interface{}),
	}

	// 设置基础挂载点
	if err := sm.setupBasicMounts(sandbox); err != nil {
		os.RemoveAll(sandboxPath)
		return nil, fmt.Errorf("failed to setup basic mounts: %w", err)
	}

	// 应用资源限制
	if err := sm.applyResourceLimits(sandbox); err != nil {
		sm.destroySandboxInternal(sandbox)
		return nil, fmt.Errorf("failed to apply resource limits: %w", err)
	}

	// 注册沙箱
	sm.sandboxes[sandboxID] = sandbox

	sm.logger.WithFields(logrus.Fields{
		"sandbox_id": sandboxID,
		"user_id":    userID,
		"session_id": sessionID,
		"path":       sandboxPath,
	}).Info("Sandbox created successfully")

	return sandbox, nil
}

// GetSandbox 获取沙箱
func (sm *SandboxManager) GetSandbox(sandboxID string) (*Sandbox, error) {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	sandbox, exists := sm.sandboxes[sandboxID]
	if !exists {
		return nil, fmt.Errorf("sandbox not found: %s", sandboxID)
	}

	// 更新最后使用时间
	sandbox.mutex.Lock()
	sandbox.LastUsed = time.Now()
	sandbox.mutex.Unlock()

	return sandbox, nil
}

// DestroySandbox 销毁沙箱
func (sm *SandboxManager) DestroySandbox(sandboxID string) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	sandbox, exists := sm.sandboxes[sandboxID]
	if !exists {
		return fmt.Errorf("sandbox not found: %s", sandboxID)
	}

	if err := sm.destroySandboxInternal(sandbox); err != nil {
		return err
	}

	delete(sm.sandboxes, sandboxID)

	sm.logger.WithField("sandbox_id", sandboxID).Info("Sandbox destroyed successfully")
	return nil
}

// ExecuteInSandbox 在沙箱中执行命令
func (sm *SandboxManager) ExecuteInSandbox(ctx context.Context, sandboxID string, command string, args []string) (*ExecutionResult, error) {
	sandbox, err := sm.GetSandbox(sandboxID)
	if err != nil {
		return nil, err
	}

	// 构建命令
	cmd := exec.CommandContext(ctx, command, args...)

	// 设置工作目录
	cmd.Dir = sandbox.Path

	// 设置环境变量
	env := make([]string, 0, len(sandbox.Environment))
	for k, v := range sandbox.Environment {
		env = append(env, fmt.Sprintf("%s=%s", k, v))
	}
	cmd.Env = env

	// 应用安全限制
	if err := sm.applySandboxSecurity(cmd, sandbox); err != nil {
		return nil, fmt.Errorf("failed to apply sandbox security: %w", err)
	}

	startTime := time.Now()

	// 执行命令
	var stdout, stderr strings.Builder
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err = cmd.Run()

	endTime := time.Now()
	duration := endTime.Sub(startTime)

	// 更新资源使用情况
	sm.updateSandboxResources(sandbox)

	// 构建结果
	result := &ExecutionResult{
		ID:        generateResultID(),
		Success:   err == nil,
		Stdout:    stdout.String(),
		Stderr:    stderr.String(),
		Duration:  duration,
		StartTime: startTime,
		EndTime:   endTime,
		Metadata: map[string]interface{}{
			"sandbox_id": sandboxID,
			"command":    command,
			"args":       args,
		},
	}

	if err != nil {
		result.Error = err.Error()
		if exitError, ok := err.(*exec.ExitError); ok {
			result.ExitCode = exitError.ExitCode()
		} else {
			result.ExitCode = -1
		}
	}

	sm.logger.WithFields(logrus.Fields{
		"sandbox_id": sandboxID,
		"command":    command,
		"success":    result.Success,
		"duration":   duration.Milliseconds(),
	}).Info("Command executed in sandbox")

	return result, nil
}

// setupBasicMounts 设置基础挂载点
func (sm *SandboxManager) setupBasicMounts(sandbox *Sandbox) error {
	// 创建基础目录
	dirs := []string{"tmp", "home", "var", "usr"}
	for _, dir := range dirs {
		dirPath := filepath.Join(sandbox.Path, dir)
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %w", dirPath, err)
		}
	}

	// 设置基础挂载点
	mounts := []SandboxMount{
		{
			Source:      "/bin",
			Target:      filepath.Join(sandbox.Path, "bin"),
			Type:        "bind",
			Options:     "ro",
			Description: "System binaries (read-only)",
		},
		{
			Source:      "/usr/bin",
			Target:      filepath.Join(sandbox.Path, "usr/bin"),
			Type:        "bind",
			Options:     "ro",
			Description: "User binaries (read-only)",
		},
		{
			Source:      "/lib",
			Target:      filepath.Join(sandbox.Path, "lib"),
			Type:        "bind",
			Options:     "ro",
			Description: "System libraries (read-only)",
		},
	}

	sandbox.Mounts = mounts
	return nil
}

// applyResourceLimits 应用资源限制
func (sm *SandboxManager) applyResourceLimits(sandbox *Sandbox) error {
	// 在实际实现中，这里应该使用cgroups或其他机制来限制资源
	// 简化实现，只记录配置
	sandbox.Metadata["max_memory_mb"] = sm.config.MaxMemoryMB
	sandbox.Metadata["max_disk_usage_mb"] = sm.config.MaxDiskUsageMB
	sandbox.Metadata["max_processes"] = sm.config.MaxProcesses

	return nil
}

// applySandboxSecurity 应用沙箱安全设置
func (sm *SandboxManager) applySandboxSecurity(cmd *exec.Cmd, sandbox *Sandbox) error {
	// 在实际实现中，这里应该设置用户权限、chroot等安全机制
	// 简化实现，只设置基本的安全环境变量

	// 限制PATH环境变量
	restrictedPath := "/usr/local/bin:/usr/bin:/bin"
	for i, env := range cmd.Env {
		if strings.HasPrefix(env, "PATH=") {
			cmd.Env[i] = "PATH=" + restrictedPath
			break
		}
	}

	return nil
}

// updateSandboxResources 更新沙箱资源使用情况
func (sm *SandboxManager) updateSandboxResources(sandbox *Sandbox) {
	sandbox.mutex.Lock()
	defer sandbox.mutex.Unlock()

	// 简化实现，模拟资源使用情况
	sandbox.Resources.MemoryMB += 1.0
	sandbox.Resources.DiskUsageMB += 0.1
	sandbox.Resources.ProcessCount = 1
	sandbox.Resources.CPUPercent = 5.0
}

// destroySandboxInternal 内部销毁沙箱
func (sm *SandboxManager) destroySandboxInternal(sandbox *Sandbox) error {
	// 清理沙箱目录
	if err := os.RemoveAll(sandbox.Path); err != nil {
		return fmt.Errorf("failed to remove sandbox directory: %w", err)
	}

	return nil
}

// cleanupRoutine 清理协程
func (sm *SandboxManager) cleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(time.Duration(sm.config.CleanupInterval) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			sm.cleanupExpiredSandboxes()
		}
	}
}

// cleanupExpiredSandboxes 清理过期沙箱
func (sm *SandboxManager) cleanupExpiredSandboxes() {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	timeout := time.Duration(sm.config.SandboxTimeout) * time.Second
	now := time.Now()

	for id, sandbox := range sm.sandboxes {
		if now.Sub(sandbox.LastUsed) > timeout {
			if err := sm.destroySandboxInternal(sandbox); err != nil {
				sm.logger.WithError(err).WithField("sandbox_id", id).Error("Failed to cleanup expired sandbox")
			} else {
				delete(sm.sandboxes, id)
				sm.logger.WithField("sandbox_id", id).Info("Expired sandbox cleaned up")
			}
		}
	}
}

// GetSandboxStatus 获取沙箱状态
func (sm *SandboxManager) GetSandboxStatus() *SandboxStatus {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	sandboxList := make([]*SandboxInfo, 0, len(sm.sandboxes))
	for _, sandbox := range sm.sandboxes {
		sandboxList = append(sandboxList, &SandboxInfo{
			ID:        sandbox.ID,
			Status:    sandbox.Status,
			CreatedAt: sandbox.CreatedAt,
			LastUsed:  sandbox.LastUsed,
			UserID:    sandbox.UserID,
			SessionID: sandbox.SessionID,
			Resources: sandbox.Resources,
		})
	}

	return &SandboxStatus{
		Enabled:    sm.isEnabled,
		TotalCount: len(sm.sandboxes),
		MaxCount:   sm.config.MaxSandboxes,
		BasePath:   sm.basePath,
		Sandboxes:  sandboxList,
	}
}

// SandboxStatus 沙箱状态
type SandboxStatus struct {
	Enabled    bool           `json:"enabled"`
	TotalCount int            `json:"total_count"`
	MaxCount   int            `json:"max_count"`
	BasePath   string         `json:"base_path"`
	Sandboxes  []*SandboxInfo `json:"sandboxes"`
}

// SandboxInfo 沙箱信息
type SandboxInfo struct {
	ID        string                `json:"id"`
	Status    string                `json:"status"`
	CreatedAt time.Time             `json:"created_at"`
	LastUsed  time.Time             `json:"last_used"`
	UserID    int64                 `json:"user_id"`
	SessionID string                `json:"session_id"`
	Resources *SandboxResourceUsage `json:"resources"`
}

// 辅助函数
func generateSandboxID() string {
	return fmt.Sprintf("sandbox_%d", time.Now().UnixNano())
}
