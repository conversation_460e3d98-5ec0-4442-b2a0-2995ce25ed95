package ai

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aiops-platform/internal/workflow"
	"github.com/sirupsen/logrus"
)

// executionEngine 执行引擎实现
type executionEngine struct {
	hostService      workflow.HostServiceInterface
	logger           *logrus.Logger
	activeExecutions map[string]*ExecutionStatus
	executionMutex   sync.RWMutex
}

// NewExecutionEngine 创建执行引擎
func NewExecutionEngine(hostService workflow.HostServiceInterface, logger *logrus.Logger) ExecutionEngine {
	return &executionEngine{
		hostService:      hostService,
		logger:           logger,
		activeExecutions: make(map[string]*ExecutionStatus),
	}
}

// ExecuteCommands 执行命令序列
func (ee *executionEngine) ExecuteCommands(ctx context.Context, commands []CommandSequence, plan *ExecutionPlan) (*ExecutionResult, error) {
	start := time.Now()
	executionID := fmt.Sprintf("exec_%d", start.Unix())

	ee.logger.WithFields(logrus.Fields{
		"execution_id":   executionID,
		"commands_count": len(commands),
		"strategy":       plan.Strategy,
	}).Info("Starting command execution")

	// 创建执行状态
	status := &ExecutionStatus{
		ID:          executionID,
		Status:      "running",
		Progress:    0.0,
		CurrentStep: 0,
		TotalSteps:  len(commands),
		StartTime:   start,
	}
	ee.setExecutionStatus(executionID, status)

	// 根据策略执行命令
	var results []CommandResult
	var err error

	switch plan.Strategy {
	case "sequential":
		results, err = ee.executeSequential(ctx, commands, plan, status)
	case "parallel":
		results, err = ee.executeParallel(ctx, commands, plan, status)
	default:
		results, err = ee.executeSequential(ctx, commands, plan, status)
	}

	// 更新执行状态
	endTime := time.Now()
	status.EndTime = &endTime
	if err != nil {
		status.Status = "failed"
	} else {
		status.Status = "completed"
		status.Progress = 1.0
	}

	// 构建执行结果
	result := &ExecutionResult{
		Success:  err == nil && ee.allCommandsSuccessful(results),
		Results:  results,
		Summary:  ee.generateExecutionSummary(results),
		Duration: time.Since(start),
		Metadata: map[string]interface{}{
			"execution_id": executionID,
			"strategy":     plan.Strategy,
			"total_steps":  len(commands),
		},
	}

	// 清理执行状态
	ee.removeExecutionStatus(executionID)

	ee.logger.WithFields(logrus.Fields{
		"execution_id": executionID,
		"success":      result.Success,
		"duration":     result.Duration,
	}).Info("Command execution completed")

	return result, err
}

// executeSequential 顺序执行命令
func (ee *executionEngine) executeSequential(ctx context.Context, commands []CommandSequence, plan *ExecutionPlan, status *ExecutionStatus) ([]CommandResult, error) {
	var results []CommandResult

	for i, cmd := range commands {
		ee.updateExecutionProgress(status.ID, i, fmt.Sprintf("executing_step_%d", cmd.Step))

		result, err := ee.executeCommand(ctx, cmd)
		results = append(results, result)

		// 检查是否应该继续执行
		if !result.Success {
			switch plan.ErrorHandling {
			case "stop_on_error":
				return results, fmt.Errorf("execution stopped due to error in step %d", cmd.Step)
			case "retry_on_error":
				// 实现重试逻辑
				retryResult, retryErr := ee.retryCommand(ctx, cmd, 3)
				if retryErr == nil {
					results[len(results)-1] = retryResult
				}
			case "continue_on_error":
				// 继续执行下一个命令
				continue
			}
		}

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			return results, ctx.Err()
		default:
		}
	}

	return results, nil
}

// executeParallel 并行执行命令
func (ee *executionEngine) executeParallel(ctx context.Context, commands []CommandSequence, plan *ExecutionPlan, status *ExecutionStatus) ([]CommandResult, error) {
	results := make([]CommandResult, len(commands))
	var wg sync.WaitGroup
	var mu sync.Mutex
	var firstError error

	for i, cmd := range commands {
		wg.Add(1)
		go func(index int, command CommandSequence) {
			defer wg.Done()

			result, err := ee.executeCommand(ctx, command)

			mu.Lock()
			results[index] = result
			if err != nil && firstError == nil {
				firstError = err
			}
			ee.updateExecutionProgress(status.ID, index+1, fmt.Sprintf("completed_step_%d", command.Step))
			mu.Unlock()
		}(i, cmd)
	}

	wg.Wait()
	return results, firstError
}

// executeCommand 执行单个命令
func (ee *executionEngine) executeCommand(ctx context.Context, cmd CommandSequence) (CommandResult, error) {
	start := time.Now()

	ee.logger.WithFields(logrus.Fields{
		"step":    cmd.Step,
		"command": cmd.Command,
		"timeout": cmd.Timeout,
	}).Debug("Executing command")

	// 创建带超时的上下文
	cmdCtx, cancel := context.WithTimeout(ctx, time.Duration(cmd.Timeout)*time.Second)
	defer cancel()

	// 这里应该调用实际的命令执行逻辑
	// 暂时返回模拟结果
	result := CommandResult{
		Step:     cmd.Step,
		Command:  cmd.Command,
		Success:  true,
		Output:   ee.simulateCommandOutput(cmd),
		Duration: time.Since(start),
		ExitCode: 0,
	}

	// 模拟一些可能的错误情况
	if ee.shouldSimulateError(cmd) {
		result.Success = false
		result.Error = "Simulated command execution error"
		result.ExitCode = 1
	}

	return result, nil
}

// retryCommand 重试命令
func (ee *executionEngine) retryCommand(ctx context.Context, cmd CommandSequence, maxRetries int) (CommandResult, error) {
	var lastResult CommandResult
	var lastError error

	for i := 0; i < maxRetries; i++ {
		ee.logger.WithFields(logrus.Fields{
			"step":    cmd.Step,
			"attempt": i + 1,
			"max":     maxRetries,
		}).Info("Retrying command")

		result, err := ee.executeCommand(ctx, cmd)
		lastResult = result
		lastError = err

		if result.Success {
			return result, nil
		}

		// 等待重试间隔
		if i < maxRetries-1 {
			time.Sleep(2 * time.Second)
		}
	}

	return lastResult, lastError
}

// simulateCommandOutput 模拟命令输出
func (ee *executionEngine) simulateCommandOutput(cmd CommandSequence) string {
	switch {
	case cmd.Command == "ping -c 4 192.168.119.84":
		return `PING 192.168.119.84 (192.168.119.84) 56(84) bytes of data.
64 bytes from 192.168.119.84: icmp_seq=1 ttl=64 time=0.123 ms
64 bytes from 192.168.119.84: icmp_seq=2 ttl=64 time=0.156 ms
64 bytes from 192.168.119.84: icmp_seq=3 ttl=64 time=0.134 ms
64 bytes from 192.168.119.84: icmp_seq=4 ttl=64 time=0.145 ms

--- 192.168.119.84 ping statistics ---
4 packets transmitted, 4 received, 0% packet loss, time 3002ms
rtt min/avg/max/mdev = 0.123/0.139/0.156/0.013 ms`

	case cmd.Command == "nc -zv 192.168.119.84 22":
		return "Connection to 192.168.119.84 22 port [tcp/ssh] succeeded!"

	case cmd.Command == "ssh -o ConnectTimeout=5 192.168.119.84 'uptime'":
		return " 14:30:25 up 5 days,  2:15,  1 user,  load average: 0.08, 0.12, 0.09"

	default:
		return fmt.Sprintf("Command executed: %s", cmd.Command)
	}
}

// shouldSimulateError 是否应该模拟错误
func (ee *executionEngine) shouldSimulateError(cmd CommandSequence) bool {
	// 模拟一些错误情况用于测试
	return false
}

// allCommandsSuccessful 检查所有命令是否成功
func (ee *executionEngine) allCommandsSuccessful(results []CommandResult) bool {
	for _, result := range results {
		if !result.Success {
			return false
		}
	}
	return true
}

// generateExecutionSummary 生成执行摘要
func (ee *executionEngine) generateExecutionSummary(results []CommandResult) string {
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	if successCount == len(results) {
		return fmt.Sprintf("所有 %d 个步骤执行成功", len(results))
	} else {
		return fmt.Sprintf("%d/%d 个步骤执行成功", successCount, len(results))
	}
}

// GetExecutionStatus 获取执行状态
func (ee *executionEngine) GetExecutionStatus(executionID string) (*ExecutionStatus, error) {
	ee.executionMutex.RLock()
	defer ee.executionMutex.RUnlock()

	status, exists := ee.activeExecutions[executionID]
	if !exists {
		return nil, fmt.Errorf("execution not found: %s", executionID)
	}

	// 返回状态副本
	statusCopy := *status
	return &statusCopy, nil
}

// CancelExecution 取消执行
func (ee *executionEngine) CancelExecution(executionID string) error {
	ee.executionMutex.Lock()
	defer ee.executionMutex.Unlock()

	status, exists := ee.activeExecutions[executionID]
	if !exists {
		return fmt.Errorf("execution not found: %s", executionID)
	}

	status.Status = "cancelled"
	delete(ee.activeExecutions, executionID)

	ee.logger.WithField("execution_id", executionID).Info("Execution cancelled")
	return nil
}

// setExecutionStatus 设置执行状态
func (ee *executionEngine) setExecutionStatus(executionID string, status *ExecutionStatus) {
	ee.executionMutex.Lock()
	defer ee.executionMutex.Unlock()
	ee.activeExecutions[executionID] = status
}

// updateExecutionProgress 更新执行进度
func (ee *executionEngine) updateExecutionProgress(executionID string, currentStep int, stepName string) {
	ee.executionMutex.Lock()
	defer ee.executionMutex.Unlock()

	if status, exists := ee.activeExecutions[executionID]; exists {
		status.CurrentStep = currentStep
		status.Progress = float64(currentStep) / float64(status.TotalSteps)
	}
}

// removeExecutionStatus 移除执行状态
func (ee *executionEngine) removeExecutionStatus(executionID string) {
	ee.executionMutex.Lock()
	defer ee.executionMutex.Unlock()
	delete(ee.activeExecutions, executionID)
}
